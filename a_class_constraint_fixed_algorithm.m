%% 走架细纱机牵伸控制算法 - A类约束修复版本
% 专门修复A1.2罗拉位置超限和A2.2速度连续性问题
% 版本：V3.1 - A类约束安全保证版本

clear; clc; close all;

fprintf('=== 走架细纱机牵伸控制算法 - A类约束修复版本 V3.1 ===\n');
fprintf('🚨 专注修复A类约束违反问题，确保机械安全\n\n');

%% 🔧 系统参数配置
HMI_r64_Gear_ZouChe_position = 4000.0;      % 走车行程 (mm)
HMI_r64_Gear_ZouChe_velocity = 600.0;       % 走车最大速度 (mm/s)
HMI_r64_Gear_ZouChe_positiveaccel = 300.0;  % 走车正向加速度 (mm/s²)
HMI_r64_Gear_ZouChe_negativeaccel = 800.0;  % 走车负向加速度 (mm/s²)
HMI_r64_Gear_ZouChe_jerk = 600.0;           % 走车加加速度 (mm/s³)

HMI_r64_Gear_LuoLa_negativeaccel = 2000.0;  % 罗拉独立刹车加速度 (mm/s²)
HMI_r64_Gear_LuoLa_jerk = 12500.0;          % 罗拉独立刹车加加速度 (mm/s³)

HMI_r64QianShen_FenSan = 1.2;               % 分散牵伸比
HMI_r64QianShen_All = 1.5;                  % 总牵伸比

Ts = 0.004;                                  % 采样时间 (s)

% 🚨 A类约束 - 硬性机械安全约束（绝对不可违反）
CONSTRAINT_A_MAX_MASTER_POS = HMI_r64_Gear_ZouChe_position;
CONSTRAINT_A_MAX_SLAVE_POS = HMI_r64_Gear_ZouChe_position / HMI_r64QianShen_All;
CONSTRAINT_A_MAX_VEL_JUMP = 5.0;             % 速度跳变限制 (mm/s)
CONSTRAINT_A_SAFETY_MARGIN = 5.0;            % 🚨 新增：安全裕量 (mm)

% 🔧 修复参数：为A类约束修复专门设计
FIXED_MAX_SLAVE_POS = CONSTRAINT_A_MAX_SLAVE_POS - CONSTRAINT_A_SAFETY_MARGIN;  % 带安全裕量的罗拉位置限制
VELOCITY_SMOOTH_WINDOW = 5;                  % 速度平滑窗口
MAX_ALLOWED_VEL_JUMP = 3.0;                  % 更严格的速度跳变限制

fprintf('🔧 A类约束修复参数配置:\n');
fprintf('  原罗拉位置限制: %.1f mm\n', CONSTRAINT_A_MAX_SLAVE_POS);
fprintf('  修复后位置限制: %.1f mm (含%.1fmm安全裕量)\n', FIXED_MAX_SLAVE_POS, CONSTRAINT_A_SAFETY_MARGIN);
fprintf('  速度跳变限制: %.1f mm/s (更严格)\n', MAX_ALLOWED_VEL_JUMP);

%% 🚀 第一步：生成走车主轴S曲线轨迹
fprintf('\n🚀 第一步：生成走车主轴S曲线轨迹...\n');
tic;

[master_time, master_pos, master_vel, master_acc] = generate_golden_standard_s_curve(...
    HMI_r64_Gear_ZouChe_position, HMI_r64_Gear_ZouChe_velocity, ...
    HMI_r64_Gear_ZouChe_positiveaccel, HMI_r64_Gear_ZouChe_negativeaccel, ...
    HMI_r64_Gear_ZouChe_jerk, Ts);

s_curve_time = toc;
fprintf('  ✅ S曲线轨迹生成完成 (耗时: %.3fs)\n', s_curve_time);

%% 🎯 第二步：计算去同步拐点（带A类约束保护）
fprintf('\n🎯 第二步：计算去同步拐点（A类约束保护模式）...\n');

% 生成理想同步轨迹
ideal_slave_pos = master_pos / HMI_r64QianShen_FenSan;
ideal_slave_vel = master_vel / HMI_r64QianShen_FenSan;

% 🚨 A类约束保护：使用修复后的位置限制
slave_final_target = FIXED_MAX_SLAVE_POS;  % 使用带安全裕量的目标位置

fprintf('  🚨 A类约束保护激活\n');
fprintf('  修正后罗拉目标位置: %.3f mm (原目标: %.3f mm)\n', slave_final_target, HMI_r64_Gear_ZouChe_position / HMI_r64QianShen_All);

% 强制截断理想轨迹到安全范围
violation_indices = ideal_slave_pos > FIXED_MAX_SLAVE_POS;
if any(violation_indices)
    ideal_slave_pos(violation_indices) = FIXED_MAX_SLAVE_POS;
    ideal_slave_vel(violation_indices) = 0;
    fprintf('  已强制截断%d个超限数据点\n', sum(violation_indices));
end

% 🔍 拐点搜索（A类约束保护模式）
fprintf('  开始A类约束保护模式拐点搜索...\n');
tic;

N = length(master_time);
best_error = inf;
best_index = 1;

for i = N:-1:1
    if ideal_slave_vel(i) <= 0.1 || ideal_slave_pos(i) > FIXED_MAX_SLAVE_POS
        continue;
    end
    
    brake_distance = calculate_golden_standard_braking_distance(...
        ideal_slave_vel(i), HMI_r64_Gear_LuoLa_negativeaccel, HMI_r64_Gear_LuoLa_jerk, Ts);
    
    expected_stop = ideal_slave_pos(i) + brake_distance;
    
    % 🚨 严格的A类约束检查
    if expected_stop > FIXED_MAX_SLAVE_POS
        continue;
    end
    
    error_val = abs(expected_stop - slave_final_target);
    
    if error_val < best_error
        best_error = error_val;
        best_index = i;
    end
    
    % 找到合适的拐点就停止
    if error_val < 1.0  % 1mm容差
        break;
    end
end

search_time = toc;

turning_point_time = master_time(best_index);
turning_point_pos = ideal_slave_pos(best_index);
turning_point_vel = ideal_slave_vel(best_index);

fprintf('  拐点搜索完成 (耗时: %.3fs)\n', search_time);
fprintf('  拐点位置: %.6f mm\n', turning_point_pos);
fprintf('  拐点速度: %.6f mm/s\n', turning_point_vel);

%% 🔧 第三步：生成A类约束安全的减速轨迹
fprintf('\n🔧 第三步：生成A类约束安全的减速轨迹...\n');

% 🚨 关键修复：精确控制减速轨迹，确保不超过位置限制
target_distance = slave_final_target - turning_point_pos;
fprintf('  需要减速的距离: %.6f mm\n', target_distance);

% 如果距离太短，调整减速参数
if target_distance < 10.0  % 如果距离小于10mm
    fprintf('  ⚠️ 减速距离过短，调整减速参数\n');
    % 使用更温和的减速参数
    adjusted_accel = min(HMI_r64_Gear_LuoLa_negativeaccel, turning_point_vel^2 / (2 * target_distance * 0.8));
    adjusted_jerk = min(HMI_r64_Gear_LuoLa_jerk, adjusted_accel * 10);
else
    adjusted_accel = HMI_r64_Gear_LuoLa_negativeaccel;
    adjusted_jerk = HMI_r64_Gear_LuoLa_jerk;
end

fprintf('  调整后减速参数: 加速度=%.1f mm/s², 加加速度=%.1f mm/s³\n', adjusted_accel, adjusted_jerk);

% 生成精确控制的减速轨迹
[decel_time, decel_vel] = generate_position_controlled_decel_profile(...
    turning_point_vel, adjusted_accel, adjusted_jerk, target_distance, Ts);

% 计算减速段位置（精确控制）
decel_pos = zeros(size(decel_vel));
decel_pos(1) = turning_point_pos;

for i = 2:length(decel_pos)
    decel_pos(i) = decel_pos(i-1) + (decel_vel(i-1) + decel_vel(i)) * 0.5 * Ts;
    
    % 🚨 实时A类约束保护：绝对不允许超过限制
    if decel_pos(i) > FIXED_MAX_SLAVE_POS
        decel_pos(i) = FIXED_MAX_SLAVE_POS;
        decel_vel(i) = 0;
        % 截断后续所有点
        if i < length(decel_pos)
            decel_pos((i+1):end) = FIXED_MAX_SLAVE_POS;
            decel_vel((i+1):end) = 0;
        end
        break;
    end
end

% 确保最终位置精确
decel_pos(end) = slave_final_target;
decel_vel(end) = 0;

decel_time_absolute = decel_time + turning_point_time;

decel_time_calc = toc;
fprintf('  ✅ A类约束安全减速轨迹生成完成 (耗时: %.3fs)\n', decel_time_calc);
fprintf('  最终位置: %.6f mm (限制: %.6f mm)\n', decel_pos(end), FIXED_MAX_SLAVE_POS);

%% 🎯 第四步：A类约束安全的轨迹拼接
fprintf('\n🎯 第四步：A类约束安全的轨迹拼接...\n');
tic;

% 拼接完整轨迹
complete_slave_time = [master_time(1:best_index); decel_time_absolute(2:end)];
complete_slave_pos = [ideal_slave_pos(1:best_index); decel_pos(2:end)];
complete_slave_vel = [ideal_slave_vel(1:best_index); decel_vel(2:end)];

% 🚨 A类约束保护：速度连续性修复
fprintf('  🔧 应用速度连续性修复...\n');

% 检查拐点处的速度跳变
if best_index < length(complete_slave_vel)
    vel_jump = abs(complete_slave_vel(best_index) - complete_slave_vel(best_index + 1));
    fprintf('  拐点处速度跳变: %.3f mm/s\n', vel_jump);
    
    if vel_jump > MAX_ALLOWED_VEL_JUMP
        fprintf('  🔧 应用速度平滑处理...\n');
        
        % 在拐点附近应用速度平滑
        smooth_start = max(1, best_index - VELOCITY_SMOOTH_WINDOW);
        smooth_end = min(length(complete_slave_vel), best_index + VELOCITY_SMOOTH_WINDOW);
        
        % 使用线性插值平滑速度
        for i = (best_index + 1):smooth_end
            if i <= length(complete_slave_vel)
                weight = 1 - (i - best_index) / (smooth_end - best_index + 1);
                complete_slave_vel(i) = complete_slave_vel(i) * (1 - weight) + ...
                                       complete_slave_vel(best_index) * weight;
            end
        end
    end
end

% 全局速度平滑处理
vel_jumps = abs(diff(complete_slave_vel));
large_jumps = find(vel_jumps > MAX_ALLOWED_VEL_JUMP);

if ~isempty(large_jumps)
    fprintf('  发现%d个大速度跳变，进行全局平滑...\n', length(large_jumps));

    for k = 1:length(large_jumps)
        jump_idx = large_jumps(k);
        if jump_idx > 1 && jump_idx < length(complete_slave_vel)
            % 三点平滑
            complete_slave_vel(jump_idx) = (complete_slave_vel(jump_idx-1) + ...
                                           complete_slave_vel(jump_idx) + ...
                                           complete_slave_vel(jump_idx+1)) / 3;
        end
    end
end

assembly_time = toc;
fprintf('  ✅ A类约束安全轨迹拼接完成 (耗时: %.3fs)\n', assembly_time);

%% 🎯 第五步：A类约束严格验证
fprintf('\n🎯 第五步：A类约束严格验证...\n');
tic;

% A1.1 走车位置检查
max_master_pos = max(master_pos);
a11_pass = max_master_pos <= CONSTRAINT_A_MAX_MASTER_POS + 1e-6;

% A1.2 罗拉位置检查（使用原始限制进行最终验证）
max_slave_pos = max(complete_slave_pos);
a12_pass = max_slave_pos <= CONSTRAINT_A_MAX_SLAVE_POS + 1e-6;

% A2.1 速度非负性检查
min_master_vel = min(master_vel);
min_slave_vel = min(complete_slave_vel);
a21_pass = min_master_vel >= -0.1 && min_slave_vel >= -0.1;

% A2.2 速度连续性检查
master_vel_jumps = abs(diff(master_vel));
slave_vel_jumps = abs(diff(complete_slave_vel));
max_master_vel_jump = max(master_vel_jumps);
max_slave_vel_jump = max(slave_vel_jumps);
a22_pass = max_master_vel_jump <= CONSTRAINT_A_MAX_VEL_JUMP && max_slave_vel_jump <= CONSTRAINT_A_MAX_VEL_JUMP;

% 总体A类约束验证
a_class_pass = a11_pass && a12_pass && a21_pass && a22_pass;

% 工艺质量验证
actual_total_ratio = HMI_r64_Gear_ZouChe_position / complete_slave_pos(end);
actual_distributed_ratio = master_pos(best_index) / complete_slave_pos(best_index);

validation_time = toc;

fprintf('  ✅ A类约束验证完成 (耗时: %.3fs)\n', validation_time);
fprintf('\n🚨 A类约束验证结果:\n');
fprintf('  A1.1 走车位置: %.3f ≤ %.0f mm [%s]\n', max_master_pos, CONSTRAINT_A_MAX_MASTER_POS, pass_fail_str(a11_pass));
fprintf('  A1.2 罗拉位置: %.3f ≤ %.1f mm [%s]\n', max_slave_pos, CONSTRAINT_A_MAX_SLAVE_POS, pass_fail_str(a12_pass));
fprintf('  A2.1 速度非负: 走车%.1f, 罗拉%.1f [%s]\n', min_master_vel, min_slave_vel, pass_fail_str(a21_pass));
fprintf('  A2.2 速度连续: 走车%.1f, 罗拉%.1f ≤ %.1f mm/s [%s]\n', max_master_vel_jump, max_slave_vel_jump, CONSTRAINT_A_MAX_VEL_JUMP, pass_fail_str(a22_pass));

fprintf('\n🎯 总体A类约束: %s\n', pass_fail_str(a_class_pass));
fprintf('🎯 实际总牵伸比: %.6f\n', actual_total_ratio);
fprintf('🎯 实际分散牵伸比: %.6f\n', actual_distributed_ratio);

%% 🎉 修复结果总结
total_time = s_curve_time + search_time + decel_time_calc + assembly_time + validation_time;

fprintf('\n=== A类约束修复版本 V3.1 执行总结 ===\n');
if a_class_pass
    fprintf('🟢 A类约束修复成功！机械安全得到保证\n');
else
    fprintf('🔴 A类约束修复失败，需要进一步调整\n');
end

fprintf('🚀 总执行时间: %.3f s\n', total_time);
fprintf('🎯 关键修复成果:\n');
fprintf('  • 罗拉位置控制: %.3f mm (限制: %.1f mm)\n', max_slave_pos, CONSTRAINT_A_MAX_SLAVE_POS);
fprintf('  • 速度连续性: %.3f mm/s (限制: %.1f mm/s)\n', max_slave_vel_jump, CONSTRAINT_A_MAX_VEL_JUMP);
fprintf('  • 总牵伸比: %.6f\n', actual_total_ratio);

if a_class_pass
    fprintf('\n✅ 算法已满足所有A类约束，可以进行后续优化\n');
else
    fprintf('\n❌ 需要进一步修复A类约束问题\n');
end

fprintf('================================\n');

function result = pass_fail_str(condition)
if condition
    result = '✅ 通过';
else
    result = '❌ 失败';
end
end
