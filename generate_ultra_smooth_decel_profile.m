function [time_vec, vel_vec] = generate_ultra_smooth_decel_profile(v0, a_decel, j_max, target_distance, min_time, Ts)
%% 超平滑减速轨迹生成器
% 专门为A2.2速度连续性约束设计，确保减速过程绝对平滑
% 输入：
%   v0 - 初始速度 (mm/s)
%   a_decel - 减速度 (mm/s²)
%   j_max - 最大加加速度 (mm/s³)
%   target_distance - 目标减速距离 (mm)
%   min_time - 最小减速时间 (s)
%   Ts - 采样时间 (s)

if v0 <= 0.001 || target_distance <= 0.001
    time_vec = 0;
    vel_vec = 0;
    return;
end

fprintf('    🔧 超平滑减速轨迹设计:\n');
fprintf('    初始速度: %.3f mm/s\n', v0);
fprintf('    目标距离: %.3f mm\n', target_distance);
fprintf('    最小时间: %.3f s\n', min_time);

% 🚨 关键：强制使用最小时间约束，确保减速足够温和
% 根据最小时间反推最大允许的减速度
max_allowed_accel = v0 / min_time;
actual_accel = min(a_decel, max_allowed_accel);

fprintf('    原减速度: %.1f mm/s²\n', a_decel);
fprintf('    限制减速度: %.1f mm/s²\n', actual_accel);

% 进一步限制加加速度，确保超平滑
actual_jerk = min(j_max, actual_accel * 2);  % 更温和的加加速度比例

fprintf('    原加加速度: %.1f mm/s³\n', j_max);
fprintf('    限制加加速度: %.1f mm/s³\n', actual_jerk);

% 🚨 使用超平滑的S曲线参数
t_j = actual_accel / actual_jerk;

% 强制使用梯形轮廓，避免三角形轮廓的突变
if v0 * actual_jerk < actual_accel^2
    % 即使是三角形轮廓，也强制转换为梯形
    t_j = sqrt(v0 / actual_jerk) * 0.7;  % 减小加加速度时间
    actual_accel = actual_jerk * t_j;
    t_const = v0 / actual_accel - 2 * t_j;
    t_const = max(t_const, min_time * 0.3);  % 确保有足够的恒减速段
else
    % 梯形轮廓
    t_const = v0 / actual_accel - 2 * t_j;
    t_const = max(t_const, min_time * 0.3);  % 确保有足够的恒减速段
end

T_total = 2 * t_j + t_const;

% 如果总时间小于最小时间，强制延长
if T_total < min_time
    fprintf('    ⚠️ 减速时间%.3fs < 最小时间%.3fs，强制延长\n', T_total, min_time);
    
    % 延长恒减速段
    additional_time = min_time - T_total;
    t_const = t_const + additional_time;
    T_total = min_time;
    
    % 重新计算减速度以匹配距离
    actual_accel = v0 / (T_total - t_j);
    actual_jerk = actual_accel / t_j;
    
    fprintf('    调整后减速度: %.1f mm/s²\n', actual_accel);
    fprintf('    调整后加加速度: %.1f mm/s³\n', actual_jerk);
end

% 时间节点
T1 = t_j;
T2 = T1 + t_const;
T3 = T2 + t_j;

fprintf('    减速时间分配: t_j1=%.3fs, t_const=%.3fs, t_j2=%.3fs\n', t_j, t_const, t_j);
fprintf('    总减速时间: %.3f s\n', T_total);

% 生成超密集的时间序列，确保平滑
time_vec = (0:Ts:T_total)';
N = length(time_vec);

% 确保最后一个时间点
if abs(time_vec(end) - T_total) > Ts/2
    time_vec = [time_vec; T_total];
    N = N + 1;
end

vel_vec = zeros(N, 1);
acc_vec = zeros(N, 1);
pos_vec = zeros(N, 1);

vel_vec(1) = v0;
pos_vec(1) = 0;

% 🚨 超平滑轨迹生成：使用更小的时间步长进行内部计算
internal_Ts = Ts / 4;  % 使用4倍精度
internal_time = (0:internal_Ts:T_total)';
internal_N = length(internal_time);

internal_vel = zeros(internal_N, 1);
internal_acc = zeros(internal_N, 1);
internal_vel(1) = v0;

% 高精度内部轨迹生成
for i = 2:internal_N
    t = internal_time(i-1);
    
    % 确定加加速度（超平滑过渡）
    if t < T1
        jerk = -actual_jerk;
    elseif t < T2
        jerk = 0;
    elseif t < T3
        jerk = actual_jerk;
    else
        jerk = 0;
    end
    
    % 平滑过渡处理
    transition_width = internal_Ts * 2;
    if abs(t - T1) < transition_width
        % T1附近的平滑过渡
        weight = 0.5 * (1 + cos(pi * (t - T1) / transition_width));
        jerk = jerk * weight;
    elseif abs(t - T2) < transition_width
        % T2附近的平滑过渡
        weight = 0.5 * (1 + cos(pi * (t - T2) / transition_width));
        jerk = jerk * weight;
    end
    
    % 更新加速度和速度
    internal_acc(i) = internal_acc(i-1) + jerk * internal_Ts;
    internal_acc(i) = max(internal_acc(i), -actual_accel * 1.1);
    internal_acc(i) = min(internal_acc(i), 0);
    
    internal_vel(i) = internal_vel(i-1) + internal_acc(i-1) * internal_Ts;
    internal_vel(i) = max(internal_vel(i), 0);
    
    % 平滑结束
    if internal_vel(i) < 0.01
        internal_vel(i) = 0;
        if i < internal_N
            internal_vel((i+1):end) = 0;
        end
        break;
    end
end

% 下采样到目标时间序列
for i = 1:N
    target_time = time_vec(i);
    [~, closest_idx] = min(abs(internal_time - target_time));
    vel_vec(i) = internal_vel(closest_idx);
    
    if i > 1
        acc_vec(i) = (vel_vec(i) - vel_vec(i-1)) / Ts;
    end
end

% 最终平滑处理
vel_vec(end) = 0;

% 应用额外的平滑滤波
if length(vel_vec) > 5
    % 使用移动平均进行最终平滑
    smoothed_vel = vel_vec;
    for i = 3:(length(vel_vec)-2)
        smoothed_vel(i) = mean(vel_vec((i-2):(i+2)));
    end
    vel_vec = smoothed_vel;
end

% 验证速度连续性
vel_jumps = abs(diff(vel_vec));
max_vel_jump = max(vel_jumps);

fprintf('    生成轨迹验证:\n');
fprintf('    数据点数: %d\n', length(vel_vec));
fprintf('    最大速度跳变: %.3f mm/s\n', max_vel_jump);
fprintf('    最终速度: %.6f mm/s\n', vel_vec(end));

% 计算实际距离
actual_distance = 0;
for i = 2:length(vel_vec)
    actual_distance = actual_distance + (vel_vec(i-1) + vel_vec(i)) * 0.5 * Ts;
end

fprintf('    实际减速距离: %.3f mm (目标: %.3f mm)\n', actual_distance, target_distance);

if max_vel_jump > 5.0
    warning('超平滑减速轨迹仍有较大速度跳变: %.3f mm/s', max_vel_jump);
end

end
