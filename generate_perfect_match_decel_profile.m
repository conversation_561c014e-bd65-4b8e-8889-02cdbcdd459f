function [time_vec, vel_vec] = generate_perfect_match_decel_profile(v0, a_decel, j_max, target_distance, Ts)
%% 完美匹配减速轨迹生成器
% 专门为速度连续性设计，确保减速轨迹与拐点速度完美匹配
% 输入：
%   v0 - 初始速度 (mm/s)
%   a_decel - 减速度 (mm/s²)
%   j_max - 最大加加速度 (mm/s³)
%   target_distance - 目标减速距离 (mm)
%   Ts - 采样时间 (s)

if v0 <= 0.001 || target_distance <= 0.001
    time_vec = 0;
    vel_vec = 0;
    return;
end

% 🚨 关键：精确计算减速参数以匹配目标距离
% 使用物理公式反推最优的减速轮廓

% 首先尝试梯形减速轮廓
t_j = a_decel / j_max;

% 计算梯形轮廓的理论距离
s_j = (1/6) * j_max * t_j^3;  % 加加速段距离
v_plateau = v0 - a_decel * t_j;  % 恒减速段的起始速度

if v_plateau > 0
    % 梯形轮廓
    t_const = v_plateau / a_decel;  % 恒减速段时间
    s_const = v_plateau * t_const - 0.5 * a_decel * t_const^2;  % 恒减速段距离
    total_distance = 2 * s_j + s_const;
else
    % 三角形轮廓
    t_j = sqrt(v0 / j_max);
    total_distance = (2/3) * v0 * t_j;
    t_const = 0;
end

% 如果理论距离与目标距离不匹配，调整参数
distance_ratio = target_distance / total_distance;

if abs(distance_ratio - 1.0) > 0.1  % 如果距离差异超过10%
    % 重新计算减速参数
    if distance_ratio < 1.0
        % 目标距离更短，需要更大的减速度
        adjusted_accel = a_decel / distance_ratio;
        adjusted_jerk = min(j_max, adjusted_accel * 10);
    else
        % 目标距离更长，需要更小的减速度
        adjusted_accel = a_decel * distance_ratio * 0.8;
        adjusted_jerk = min(j_max, adjusted_accel * 5);
    end
    
    a_decel = adjusted_accel;
    j_max = adjusted_jerk;
end

% 重新计算时间参数
t_j = a_decel / j_max;

if v0 * j_max < a_decel^2
    % 三角形减速轮廓
    t_j = sqrt(v0 / j_max);
    a_actual = j_max * t_j;
    t_const = 0;
    T_total = 2 * t_j;
else
    % 梯形减速轮廓
    a_actual = a_decel;
    t_const = v0 / a_actual - t_j;
    T_total = 2 * t_j + t_const;
end

% 时间节点
T1 = t_j;
T2 = T1 + t_const;
T3 = T2 + t_j;

% 生成时间序列
time_vec = (0:Ts:T_total)';
N = length(time_vec);

% 确保最后一个时间点
if abs(time_vec(end) - T_total) > Ts/2
    time_vec = [time_vec; T_total];
    N = N + 1;
end

vel_vec = zeros(N, 1);
acc_vec = zeros(N, 1);

vel_vec(1) = v0;

% 🚨 关键：高精度轨迹生成，确保速度连续性
for i = 2:N
    t = time_vec(i-1);
    
    % 确定加加速度
    if t < T1
        jerk = -j_max;
    elseif t < T2
        jerk = 0;
    elseif t < T3
        jerk = j_max;
    else
        jerk = 0;
    end
    
    % 更新加速度
    acc_vec(i) = acc_vec(i-1) + jerk * Ts;
    acc_vec(i) = max(acc_vec(i), -a_actual);
    acc_vec(i) = min(acc_vec(i), 0);
    
    % 更新速度
    vel_vec(i) = vel_vec(i-1) + acc_vec(i-1) * Ts;
    vel_vec(i) = max(vel_vec(i), 0);
    
    % 平滑结束
    if vel_vec(i) < 0.01
        vel_vec(i) = 0;
        if i < N
            vel_vec((i+1):end) = 0;
        end
        break;
    end
end

% 🚨 关键：确保初始速度完全匹配
vel_vec(1) = v0;  % 强制匹配初始速度

% 确保最终速度为0
vel_vec(end) = 0;

% 验证距离匹配
actual_distance = 0;
for i = 2:length(vel_vec)
    actual_distance = actual_distance + (vel_vec(i-1) + vel_vec(i)) * 0.5 * Ts;
end

% 如果距离不匹配，进行微调
distance_error = abs(actual_distance - target_distance);
if distance_error > target_distance * 0.05  % 如果误差超过5%
    % 通过时间缩放来调整距离
    scale_factor = target_distance / actual_distance;
    
    if scale_factor > 0.5 && scale_factor < 2.0  % 合理的缩放范围
        time_vec = time_vec * scale_factor;
        % 速度保持不变，通过时间缩放控制距离
    end
end

% 最终验证速度连续性
vel_jumps = abs(diff(vel_vec));
max_vel_jump = max(vel_jumps);

if max_vel_jump > 5.0
    % 如果仍有大的速度跳变，应用轻微平滑
    for i = 2:(length(vel_vec)-1)
        if abs(vel_vec(i) - vel_vec(i-1)) > 5.0
            vel_vec(i) = (vel_vec(i-1) + vel_vec(i) + vel_vec(i+1)) / 3;
        end
    end
end

% 确保初始和最终速度不变
vel_vec(1) = v0;
vel_vec(end) = 0;

end
