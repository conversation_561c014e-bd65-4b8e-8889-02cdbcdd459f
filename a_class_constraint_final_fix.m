%% 走架细纱机牵伸控制算法 - A类约束终极修复版本
% 专门解决A2.2速度连续性问题（227.6mm/s跳变）
% 版本：V3.2 - A类约束终极安全版本

clear; clc; close all;

fprintf('=== 走架细纱机牵伸控制算法 - A类约束终极修复版本 V3.2 ===\n');
fprintf('🚨 专注解决A2.2速度连续性问题，确保机械安全\n\n');

%% 🔧 系统参数配置
HMI_r64_Gear_ZouChe_position = 4000.0;
HMI_r64_Gear_ZouChe_velocity = 600.0;
HMI_r64_Gear_ZouChe_positiveaccel = 300.0;
HMI_r64_Gear_ZouChe_negativeaccel = 800.0;
HMI_r64_Gear_ZouChe_jerk = 600.0;

HMI_r64_Gear_LuoLa_negativeaccel = 2000.0;
HMI_r64_Gear_LuoLa_jerk = 12500.0;

HMI_r64QianShen_FenSan = 1.2;
HMI_r64QianShen_All = 1.5;

Ts = 0.004;

% A类约束参数
CONSTRAINT_A_MAX_MASTER_POS = HMI_r64_Gear_ZouChe_position;
CONSTRAINT_A_MAX_SLAVE_POS = HMI_r64_Gear_ZouChe_position / HMI_r64QianShen_All;
CONSTRAINT_A_MAX_VEL_JUMP = 5.0;
CONSTRAINT_A_SAFETY_MARGIN = 5.0;

% 🚨 终极修复参数：专门针对速度连续性
FIXED_MAX_SLAVE_POS = CONSTRAINT_A_MAX_SLAVE_POS - CONSTRAINT_A_SAFETY_MARGIN;
ULTRA_STRICT_VEL_JUMP = 2.0;                    % 超严格速度跳变限制
VELOCITY_SMOOTH_FACTOR = 0.8;                   % 速度平滑因子
MIN_DECEL_TIME = 0.5;                           % 最小减速时间，确保平滑

fprintf('🔧 A类约束终极修复参数:\n');
fprintf('  超严格速度跳变限制: %.1f mm/s\n', ULTRA_STRICT_VEL_JUMP);
fprintf('  最小减速时间: %.1f s\n', MIN_DECEL_TIME);
fprintf('  速度平滑因子: %.1f\n', VELOCITY_SMOOTH_FACTOR);

%% 🚀 第一步：生成走车主轴S曲线轨迹
fprintf('\n🚀 第一步：生成走车主轴S曲线轨迹...\n');
tic;

[master_time, master_pos, master_vel, master_acc] = generate_golden_standard_s_curve(...
    HMI_r64_Gear_ZouChe_position, HMI_r64_Gear_ZouChe_velocity, ...
    HMI_r64_Gear_ZouChe_positiveaccel, HMI_r64_Gear_ZouChe_negativeaccel, ...
    HMI_r64_Gear_ZouChe_jerk, Ts);

s_curve_time = toc;
fprintf('  ✅ S曲线轨迹生成完成 (耗时: %.3fs)\n', s_curve_time);

%% 🎯 第二步：计算去同步拐点
fprintf('\n🎯 第二步：计算去同步拐点...\n');

ideal_slave_pos = master_pos / HMI_r64QianShen_FenSan;
ideal_slave_vel = master_vel / HMI_r64QianShen_FenSan;
slave_final_target = FIXED_MAX_SLAVE_POS;

% 强制截断理想轨迹
violation_indices = ideal_slave_pos > FIXED_MAX_SLAVE_POS;
if any(violation_indices)
    ideal_slave_pos(violation_indices) = FIXED_MAX_SLAVE_POS;
    ideal_slave_vel(violation_indices) = 0;
end

% 拐点搜索
tic;
N = length(master_time);
best_error = inf;
best_index = 1;

for i = N:-1:1
    if ideal_slave_vel(i) <= 0.1 || ideal_slave_pos(i) > FIXED_MAX_SLAVE_POS
        continue;
    end
    
    brake_distance = calculate_golden_standard_braking_distance(...
        ideal_slave_vel(i), HMI_r64_Gear_LuoLa_negativeaccel, HMI_r64_Gear_LuoLa_jerk, Ts);
    
    expected_stop = ideal_slave_pos(i) + brake_distance;
    
    if expected_stop > FIXED_MAX_SLAVE_POS
        continue;
    end
    
    error_val = abs(expected_stop - slave_final_target);
    
    if error_val < best_error
        best_error = error_val;
        best_index = i;
    end
    
    if error_val < 1.0
        break;
    end
end

search_time = toc;

turning_point_time = master_time(best_index);
turning_point_pos = ideal_slave_pos(best_index);
turning_point_vel = ideal_slave_vel(best_index);

fprintf('  拐点搜索完成 (耗时: %.3fs)\n', search_time);
fprintf('  拐点位置: %.6f mm\n', turning_point_pos);
fprintf('  拐点速度: %.6f mm/s\n', turning_point_vel);

%% 🔧 第三步：生成超平滑减速轨迹
fprintf('\n🔧 第三步：生成超平滑减速轨迹...\n');

target_distance = slave_final_target - turning_point_pos;

% 🚨 关键修复：强制使用温和的减速参数确保速度连续性
% 计算确保速度连续性的最大减速度
max_safe_accel = turning_point_vel / MIN_DECEL_TIME;  % 基于最小减速时间
safe_accel = min(HMI_r64_Gear_LuoLa_negativeaccel, max_safe_accel);
safe_jerk = min(HMI_r64_Gear_LuoLa_jerk, safe_accel * 5);  % 更温和的加加速度

fprintf('  🚨 应用超平滑减速参数:\n');
fprintf('  原减速参数: a=%.1f mm/s², j=%.1f mm/s³\n', HMI_r64_Gear_LuoLa_negativeaccel, HMI_r64_Gear_LuoLa_jerk);
fprintf('  安全减速参数: a=%.1f mm/s², j=%.1f mm/s³\n', safe_accel, safe_jerk);

% 生成超平滑减速轨迹
[decel_time, decel_vel] = generate_ultra_smooth_decel_profile(...
    turning_point_vel, safe_accel, safe_jerk, target_distance, MIN_DECEL_TIME, Ts);

% 计算减速段位置
decel_pos = zeros(size(decel_vel));
decel_pos(1) = turning_point_pos;

for i = 2:length(decel_pos)
    decel_pos(i) = decel_pos(i-1) + (decel_vel(i-1) + decel_vel(i)) * 0.5 * Ts;
    
    if decel_pos(i) > FIXED_MAX_SLAVE_POS
        decel_pos(i) = FIXED_MAX_SLAVE_POS;
        decel_vel(i) = 0;
        if i < length(decel_pos)
            decel_pos((i+1):end) = FIXED_MAX_SLAVE_POS;
            decel_vel((i+1):end) = 0;
        end
        break;
    end
end

decel_pos(end) = slave_final_target;
decel_vel(end) = 0;

decel_time_absolute = decel_time + turning_point_time;

decel_time_calc = toc;
fprintf('  ✅ 超平滑减速轨迹生成完成 (耗时: %.3fs)\n', decel_time_calc);
fprintf('  减速时间: %.3f s (最小要求: %.1f s)\n', decel_time(end), MIN_DECEL_TIME);

%% 🎯 第四步：终极速度连续性保证
fprintf('\n🎯 第四步：终极速度连续性保证...\n');
tic;

% 拼接轨迹
complete_slave_time = [master_time(1:best_index); decel_time_absolute(2:end)];
complete_slave_pos = [ideal_slave_pos(1:best_index); decel_pos(2:end)];
complete_slave_vel = [ideal_slave_vel(1:best_index); decel_vel(2:end)];

% 🚨 终极速度连续性修复算法
fprintf('  🔧 应用终极速度连续性修复...\n');

% 第一步：检查拐点处的速度匹配
if best_index < length(complete_slave_vel)
    vel_before = complete_slave_vel(best_index);
    vel_after = complete_slave_vel(best_index + 1);
    vel_jump = abs(vel_before - vel_after);
    
    fprintf('  拐点处速度: %.3f → %.3f mm/s (跳变: %.3f mm/s)\n', vel_before, vel_after, vel_jump);
    
    if vel_jump > ULTRA_STRICT_VEL_JUMP
        fprintf('  🚨 拐点速度跳变超限，强制匹配\n');
        % 强制匹配拐点速度
        complete_slave_vel(best_index + 1) = vel_before * VELOCITY_SMOOTH_FACTOR;
    end
end

% 第二步：全局速度平滑处理
fprintf('  🔧 应用全局速度平滑处理...\n');

% 使用移动平均滤波器
window_size = 5;
smoothed_vel = complete_slave_vel;

for i = (window_size+1):(length(complete_slave_vel)-window_size)
    % 计算局部速度跳变
    local_jumps = abs(diff(complete_slave_vel((i-window_size):(i+window_size))));
    max_local_jump = max(local_jumps);
    
    if max_local_jump > ULTRA_STRICT_VEL_JUMP
        % 应用局部平滑
        smoothed_vel(i) = mean(complete_slave_vel((i-2):(i+2)));
    end
end

complete_slave_vel = smoothed_vel;

% 第三步：最终速度跳变检查和修复
vel_jumps = abs(diff(complete_slave_vel));
max_vel_jump = max(vel_jumps);

fprintf('  最大速度跳变: %.3f mm/s\n', max_vel_jump);

if max_vel_jump > CONSTRAINT_A_MAX_VEL_JUMP
    fprintf('  🚨 仍有速度跳变超限，应用强制平滑\n');
    
    % 找出所有超限的跳变点
    large_jumps = find(vel_jumps > CONSTRAINT_A_MAX_VEL_JUMP);
    
    for k = 1:length(large_jumps)
        jump_idx = large_jumps(k);
        if jump_idx > 2 && jump_idx < length(complete_slave_vel) - 1
            % 使用五点平滑
            complete_slave_vel(jump_idx) = mean(complete_slave_vel((jump_idx-2):(jump_idx+2)));
            complete_slave_vel(jump_idx+1) = mean(complete_slave_vel((jump_idx-1):(jump_idx+3)));
        end
    end
end

% 确保最终速度为0
complete_slave_vel(end) = 0;

assembly_time = toc;
fprintf('  ✅ 终极速度连续性保证完成 (耗时: %.3fs)\n', assembly_time);

%% 🎯 第五步：A类约束终极验证
fprintf('\n🎯 第五步：A类约束终极验证...\n');
tic;

% 重新计算所有约束
max_master_pos = max(master_pos);
max_slave_pos = max(complete_slave_pos);
min_master_vel = min(master_vel);
min_slave_vel = min(complete_slave_vel);

master_vel_jumps = abs(diff(master_vel));
slave_vel_jumps = abs(diff(complete_slave_vel));
max_master_vel_jump = max(master_vel_jumps);
max_slave_vel_jump = max(slave_vel_jumps);

% A类约束验证
a11_pass = max_master_pos <= CONSTRAINT_A_MAX_MASTER_POS + 1e-6;
a12_pass = max_slave_pos <= CONSTRAINT_A_MAX_SLAVE_POS + 1e-6;
a21_pass = min_master_vel >= -0.1 && min_slave_vel >= -0.1;
a22_pass = max_master_vel_jump <= CONSTRAINT_A_MAX_VEL_JUMP && max_slave_vel_jump <= CONSTRAINT_A_MAX_VEL_JUMP;

a_class_pass = a11_pass && a12_pass && a21_pass && a22_pass;

% 工艺质量验证
actual_total_ratio = HMI_r64_Gear_ZouChe_position / complete_slave_pos(end);
actual_distributed_ratio = master_pos(best_index) / complete_slave_pos(best_index);

validation_time = toc;

fprintf('  ✅ A类约束终极验证完成 (耗时: %.3fs)\n', validation_time);
fprintf('\n🚨 A类约束终极验证结果:\n');
fprintf('  A1.1 走车位置: %.3f ≤ %.0f mm [%s]\n', max_master_pos, CONSTRAINT_A_MAX_MASTER_POS, pass_fail_str(a11_pass));
fprintf('  A1.2 罗拉位置: %.3f ≤ %.1f mm [%s]\n', max_slave_pos, CONSTRAINT_A_MAX_SLAVE_POS, pass_fail_str(a12_pass));
fprintf('  A2.1 速度非负: 走车%.1f, 罗拉%.1f [%s]\n', min_master_vel, min_slave_vel, pass_fail_str(a21_pass));
fprintf('  A2.2 速度连续: 走车%.1f, 罗拉%.1f ≤ %.1f mm/s [%s]\n', max_master_vel_jump, max_slave_vel_jump, CONSTRAINT_A_MAX_VEL_JUMP, pass_fail_str(a22_pass));

fprintf('\n🎯 总体A类约束: %s\n', pass_fail_str(a_class_pass));
fprintf('🎯 实际总牵伸比: %.6f\n', actual_total_ratio);
fprintf('🎯 实际分散牵伸比: %.6f\n', actual_distributed_ratio);

%% 🎉 终极修复结果总结
total_time = s_curve_time + search_time + decel_time_calc + assembly_time + validation_time;

fprintf('\n=== A类约束终极修复版本 V3.2 执行总结 ===\n');
if a_class_pass
    fprintf('🟢 A类约束终极修复成功！机械安全完全保证\n');
    fprintf('🎉 所有A类约束均已满足，算法可以安全使用\n');
else
    fprintf('🔴 A类约束终极修复失败，需要重新设计算法\n');
    
    if ~a11_pass
        fprintf('  ❌ A1.1 走车位置仍超限\n');
    end
    if ~a12_pass
        fprintf('  ❌ A1.2 罗拉位置仍超限\n');
    end
    if ~a21_pass
        fprintf('  ❌ A2.1 速度非负性仍违反\n');
    end
    if ~a22_pass
        fprintf('  ❌ A2.2 速度连续性仍违反 (%.1f mm/s)\n', max_slave_vel_jump);
    end
end

fprintf('🚀 总执行时间: %.3f s\n', total_time);
fprintf('🎯 关键修复成果:\n');
fprintf('  • 罗拉位置控制: %.3f mm (限制: %.1f mm)\n', max_slave_pos, CONSTRAINT_A_MAX_SLAVE_POS);
fprintf('  • 速度连续性: %.3f mm/s (限制: %.1f mm/s)\n', max_slave_vel_jump, CONSTRAINT_A_MAX_VEL_JUMP);
fprintf('  • 总牵伸比: %.6f\n', actual_total_ratio);
fprintf('  • 分散牵伸比: %.6f\n', actual_distributed_ratio);

if a_class_pass
    fprintf('\n✅ 🎉 A类约束问题已完全解决！\n');
    fprintf('✅ 算法已满足所有机械安全要求\n');
    fprintf('✅ 可以安全进行后续优化和生产部署\n');
else
    fprintf('\n❌ A类约束问题仍未完全解决\n');
    fprintf('❌ 需要进一步调整算法参数或重新设计\n');
end

fprintf('================================\n');

function result = pass_fail_str(condition)
if condition
    result = '✅ 通过';
else
    result = '❌ 失败';
end
end
