%% 验证所有加减速都采用S曲线
clear; clc;

fprintf('=== 验证所有加减速都采用平滑过渡的S曲线 ===\n');

%% 参数设置
stroke = 4000.0;
max_speed = 600.0;
accel_pos = 300.0;
accel_neg = 800.0;
jerk = 600.0;
luola_accel = 2000.0;
luola_jerk = 12500.0;
ratio_distributed = 1.2;
ratio_total = 1.5;
Ts = 0.004;

%% 测试1：走车轨迹S曲线验证
fprintf('\n1. 走车轨迹S曲线验证:\n');

% 生成走车轨迹
[master_time, master_pos, master_vel] = generate_ultra_precise_s_curve(stroke, max_speed, accel_pos, accel_neg, jerk, Ts);

% 计算加速度和加加速度
master_acc = [0; diff(master_vel) / Ts];
master_jerk = [0; diff(master_acc) / Ts];

% 检查加加速度是否在限制范围内
max_jerk_pos = max(master_jerk);
min_jerk_neg = min(master_jerk);
jerk_violations = sum(abs(master_jerk) > jerk * 1.05);

fprintf('  最大正加加速度: %.2f mm/s³ (限制: %.0f mm/s³)\n', max_jerk_pos, jerk);
fprintf('  最大负加加速度: %.2f mm/s³ (限制: %.0f mm/s³)\n', -min_jerk_neg, jerk);
fprintf('  加加速度超限点数: %d\n', jerk_violations);

if jerk_violations == 0
    fprintf('  ✅ 走车轨迹完全符合S曲线要求\n');
else
    fprintf('  ❌ 走车轨迹存在非S曲线加减速\n');
end

%% 测试2：罗拉减速轨迹S曲线验证
fprintf('\n2. 罗拉减速轨迹S曲线验证:\n');

% 模拟拐点速度
turning_point_vel = 300.0;

% 生成罗拉减速轨迹
[decel_time_rel, decel_vel] = generate_ultra_precise_decel(turning_point_vel, luola_accel, luola_jerk, Ts);

if length(decel_vel) > 1
    % 计算减速段的加速度和加加速度
    decel_acc = [0; diff(decel_vel) / Ts];
    decel_jerk = [0; diff(decel_acc) / Ts];
    
    % 检查加加速度
    max_decel_jerk_pos = max(decel_jerk);
    min_decel_jerk_neg = min(decel_jerk);
    decel_jerk_violations = sum(abs(decel_jerk) > luola_jerk * 1.05);
    
    fprintf('  减速段最大正加加速度: %.2f mm/s³ (限制: %.0f mm/s³)\n', max_decel_jerk_pos, luola_jerk);
    fprintf('  减速段最大负加加速度: %.2f mm/s³ (限制: %.0f mm/s³)\n', -min_decel_jerk_neg, luola_jerk);
    fprintf('  减速段加加速度超限点数: %d\n', decel_jerk_violations);
    
    if decel_jerk_violations == 0
        fprintf('  ✅ 罗拉减速轨迹完全符合S曲线要求\n');
    else
        fprintf('  ❌ 罗拉减速轨迹存在非S曲线加减速\n');
    end
else
    fprintf('  ⚠️  减速轨迹太短，无法验证\n');
end

%% 测试3：检查是否存在急停或线性减速
fprintf('\n3. 急停和线性减速检查:\n');

% 检查走车轨迹的速度变化
vel_changes = abs(diff(master_vel));
max_vel_change = max(vel_changes);
dangerous_changes = sum(vel_changes > 50);  % 单步变化超过50mm/s

fprintf('  走车最大单步速度变化: %.3f mm/s\n', max_vel_change);
fprintf('  危险速度变化点数: %d\n', dangerous_changes);

% 检查加速度的连续性
acc_changes = abs(diff(master_acc));
max_acc_change = max(acc_changes);
acc_discontinuities = sum(acc_changes > accel_pos * 0.1);  % 加速度变化超过10%

fprintf('  走车最大加速度跳变: %.3f mm/s²\n', max_acc_change);
fprintf('  加速度不连续点数: %d\n', acc_discontinuities);

if dangerous_changes == 0 && acc_discontinuities == 0
    fprintf('  ✅ 无急停或线性减速\n');
else
    fprintf('  ❌ 存在急停或线性减速\n');
end

%% 测试4：S曲线特征验证
fprintf('\n4. S曲线特征验证:\n');

% 检查加加速度的典型S曲线模式
% S曲线应该有：正加加速度 -> 0 -> 负加加速度 -> 0 -> 负加加速度 -> 0 -> 正加加速度

% 找到加加速度的符号变化点
jerk_sign_changes = 0;
for i = 2:length(master_jerk)
    if sign(master_jerk(i)) ~= sign(master_jerk(i-1)) && abs(master_jerk(i)) > 1e-6 && abs(master_jerk(i-1)) > 1e-6
        jerk_sign_changes = jerk_sign_changes + 1;
    end
end

fprintf('  加加速度符号变化次数: %d\n', jerk_sign_changes);

% 理想的S曲线应该有6次符号变化（7段）
if jerk_sign_changes >= 4 && jerk_sign_changes <= 8
    fprintf('  ✅ 符合S曲线特征\n');
else
    fprintf('  ⚠️  加加速度变化模式异常\n');
end

%% 测试5：机械安全性综合评估
fprintf('\n5. 机械安全性综合评估:\n');

% 所有检查项目
checks = {
    jerk_violations == 0, '走车加加速度限制';
    dangerous_changes == 0, '无危险急停';
    acc_discontinuities == 0, '加速度连续性';
    jerk_sign_changes >= 4 && jerk_sign_changes <= 8, 'S曲线特征';
};

if exist('decel_jerk_violations', 'var')
    checks{end+1,1} = decel_jerk_violations == 0;
    checks{end,2} = '罗拉加加速度限制';
end

% 计算通过率
passed_checks = sum([checks{:,1}]);
total_checks = size(checks, 1);
pass_rate = passed_checks / total_checks * 100;

fprintf('  检查项目:\n');
for i = 1:size(checks, 1)
    if checks{i,1}
        fprintf('    ✅ %s\n', checks{i,2});
    else
        fprintf('    ❌ %s\n', checks{i,2});
    end
end

fprintf('\n=== S曲线合规性总结 ===\n');
fprintf('通过检查: %d/%d (%.1f%%)\n', passed_checks, total_checks, pass_rate);

if pass_rate >= 90
    fprintf('🎉 所有加减速都采用平滑过渡的S曲线！\n');
    fprintf('✅ 算法完全符合机械安全要求\n');
elseif pass_rate >= 70
    fprintf('⚠️  大部分加减速采用S曲线，但仍有改进空间\n');
else
    fprintf('❌ 存在非S曲线的加减速，需要进一步修复\n');
end

fprintf('================================\n');

%% 包含必要的函数（简化版，用于测试）
function [time, pos, vel] = generate_ultra_precise_s_curve(dist, v_max, a_accel, a_decel, j_max, Ts)
% 调用主算法
run('ultra_precise_algorithm.m');
% 这里实际上会被主算法的函数覆盖
end

function [time, vel] = generate_ultra_precise_decel(v0, a_decel, j_max, Ts)
% 调用主算法
run('ultra_precise_algorithm.m');
% 这里实际上会被主算法的函数覆盖
end
