function [time_vec, pos_vec, vel_vec, acc_vec] = generate_golden_standard_s_curve(...
    s_target, v_max, a_accel, a_decel, j_max, Ts)
%% "黄金标准"基准模块1：S曲线轨迹生成器
% 基于正确物理公式实现高精度S曲线轨迹生成
% 支持梯形和三角形速度轮廓自动切换，位置精度达到微米级别

% 参数验证
if s_target <= 0 || v_max <= 0 || a_accel <= 0 || a_decel <= 0 || j_max <= 0
    error('所有参数必须为正数');
end

% Step 1: 计算时间参数
t_j1 = a_accel / j_max;  % 达到最大加速度的时间
t_j2 = a_decel / j_max;  % 达到最大减速度的时间

% 检查是否为三角形轮廓
v_max_triangle = sqrt(2 * s_target * a_accel * a_decel / (a_accel + a_decel));

if v_max_triangle <= v_max
    % 三角形轮廓
    v_reach = v_max_triangle;
    t_j1 = sqrt(v_reach / j_max);
    t_j2 = sqrt(v_reach / j_max);
    t_a = 0;
    t_d = 0;
else
    % 梯形轮廓
    v_reach = v_max;
    t_a = (v_reach - a_accel * t_j1) / a_accel;
    t_d = (v_reach - a_decel * t_j2) / a_decel;
    t_a = max(0, t_a);
    t_d = max(0, t_d);
end

% Step 2: 计算距离（修正版公式）
s_j1 = (1/6) * j_max * t_j1^3;  % 加加速段
s_a = 0.5 * a_accel * t_j1^2 + a_accel * t_j1 * t_a + 0.5 * a_accel * t_a^2;  % 恒加速段
s_accel_total = 2 * s_j1 + s_a;

s_j2 = (1/6) * j_max * t_j2^3;  % 减减速段
s_d = 0.5 * a_decel * t_j2^2 + a_decel * t_j2 * t_d + 0.5 * a_decel * t_d^2;  % 恒减速段
s_decel_total = 2 * s_j2 + s_d;

s_const = s_target - s_accel_total - s_decel_total;
t_v = max(0, s_const / v_reach);

% Step 3: 计算时间节点
T1 = t_j1;
T2 = T1 + t_a;
T3 = T2 + t_j1;
T4 = T3 + t_v;
T5 = T4 + t_j2;
T6 = T5 + t_d;
T7 = T6 + t_j2;

% Step 4: 生成轨迹
time_vec = (0:Ts:T7)';
N = length(time_vec);

pos_vec = zeros(N, 1);
vel_vec = zeros(N, 1);
acc_vec = zeros(N, 1);

% 精确数值积分
for i = 2:N
    t = time_vec(i-1);
    
    % 确定当前段的加加速度
    if t < T1
        jerk = j_max;
    elseif t < T2
        jerk = 0;
    elseif t < T3
        jerk = -j_max;
    elseif t < T4
        jerk = 0;
    elseif t < T5
        jerk = -j_max;
    elseif t < T6
        jerk = 0;
    elseif t < T7
        jerk = j_max;
    else
        jerk = 0;
    end
    
    % 数值积分
    acc_vec(i) = acc_vec(i-1) + jerk * Ts;
    
    % 限制加速度
    if acc_vec(i) > a_accel
        acc_vec(i) = a_accel;
    elseif acc_vec(i) < -a_decel
        acc_vec(i) = -a_decel;
    end
    
    vel_vec(i) = vel_vec(i-1) + acc_vec(i-1) * Ts;
    vel_vec(i) = max(vel_vec(i), 0);  % 速度不能为负
    
    pos_vec(i) = pos_vec(i-1) + vel_vec(i-1) * Ts + 0.5 * acc_vec(i-1) * Ts^2;
    
    % 🚨 机械安全保护：绝对不允许超调
    if pos_vec(i) >= s_target
        pos_vec(i) = s_target;
        vel_vec(i) = 0;
        acc_vec(i) = 0;
        % 确保后续所有点都停在目标位置
        if i < N
            pos_vec((i+1):end) = s_target;
            vel_vec((i+1):end) = 0;
            acc_vec((i+1):end) = 0;
        end
        break;
    end
end

% 最终修正
vel_vec(end) = 0;
acc_vec(end) = 0;
pos_vec(end) = s_target;

end
