%% 测试走车轨迹物理约束修复效果
clear; clc;

fprintf('=== 测试走车轨迹物理约束修复效果 ===\n');

%% 参数设置
stroke = 4000.0;
max_speed = 600.0;
accel_pos = 300.0;
accel_neg = 800.0;
jerk = 600.0;
Ts = 0.004;

%% 生成走车轨迹
[master_time, master_pos, master_vel] = generate_ultra_precise_s_curve(stroke, max_speed, accel_pos, accel_neg, jerk, Ts);

fprintf('走车轨迹分析:\n');
fprintf('  总时长: %.6f s\n', master_time(end));
fprintf('  总点数: %d\n', length(master_time));
fprintf('  起始位置: %.8f mm\n', master_pos(1));
fprintf('  最终位置: %.8f mm\n', master_pos(end));
fprintf('  目标位置: %.0f mm\n', stroke);
fprintf('  位置误差: %.8f mm\n', abs(master_pos(end) - stroke));

%% 检查位置单调性
pos_diff = diff(master_pos);
negative_pos_change = sum(pos_diff < -1e-10);
fprintf('  位置回退点数: %d\n', negative_pos_change);

if negative_pos_change > 0
    fprintf('  ❌ 检测到位置回退！\n');
    % 找到回退点
    backtrack_indices = find(pos_diff < -1e-10);
    fprintf('  回退发生在时间点: ');
    for i = 1:min(5, length(backtrack_indices))
        fprintf('%.3fs ', master_time(backtrack_indices(i)));
    end
    fprintf('\n');
else
    fprintf('  ✅ 位置严格单调递增\n');
end

%% 检查速度约束
fprintf('\n速度分析:\n');
fprintf('  起始速度: %.3f mm/s\n', master_vel(1));
fprintf('  最大速度: %.3f mm/s\n', max(master_vel));
fprintf('  最终速度: %.3f mm/s\n', master_vel(end));
fprintf('  最小速度: %.3f mm/s\n', min(master_vel));

negative_vel_count = sum(master_vel < -1e-10);
fprintf('  负速度点数: %d\n', negative_vel_count);

if negative_vel_count > 0
    fprintf('  ❌ 检测到负速度！\n');
    negative_indices = find(master_vel < -1e-10);
    fprintf('  负速度发生在时间点: ');
    for i = 1:min(5, length(negative_indices))
        fprintf('%.3fs ', master_time(negative_indices(i)));
    end
    fprintf('\n');
else
    fprintf('  ✅ 速度始终非负\n');
end

%% 检查位置是否超过目标
max_pos = max(master_pos);
fprintf('\n位置约束检查:\n');
fprintf('  最大位置: %.8f mm\n', max_pos);
fprintf('  目标位置: %.0f mm\n', stroke);
fprintf('  超调量: %.8f mm\n', max_pos - stroke);

if max_pos > stroke + 1e-6
    fprintf('  ❌ 位置超调！\n');
    overshoot_indices = find(master_pos > stroke + 1e-6);
    fprintf('  超调发生在时间: %.3fs - %.3fs\n', ...
        master_time(overshoot_indices(1)), master_time(overshoot_indices(end)));
else
    fprintf('  ✅ 位置未超调\n');
end

%% 输出关键时间点的数据
fprintf('\n关键时间点数据:\n');
n = length(master_time);
key_indices = [1, round(n*0.25), round(n*0.5), round(n*0.75), round(n*0.9), n];

for i = key_indices
    if i <= n
        fprintf('  t=%.3fs: pos=%.2f mm, vel=%.2f mm/s\n', ...
            master_time(i), master_pos(i), master_vel(i));
    end
end

%% 物理一致性检查
fprintf('\n物理一致性检查:\n');

% 检查速度和位置的一致性
vel_from_pos = [0; diff(master_pos) / Ts];
vel_error = abs(master_vel - vel_from_pos);
max_vel_error = max(vel_error);
fprintf('  速度-位置一致性误差: %.6f mm/s\n', max_vel_error);

if max_vel_error > 0.1
    fprintf('  ❌ 速度与位置不一致！\n');
else
    fprintf('  ✅ 速度与位置一致\n');
end

%% 总结
fprintf('\n=== 物理约束验证总结 ===\n');
constraints_ok = (negative_pos_change == 0) && (negative_vel_count == 0) && ...
                 (max_pos <= stroke + 1e-6) && (max_vel_error <= 0.1);

if constraints_ok
    fprintf('🎉 所有物理约束满足！\n');
    fprintf('✅ 位置单调递增\n');
    fprintf('✅ 速度始终非负\n');
    fprintf('✅ 无位置超调\n');
    fprintf('✅ 速度位置一致\n');
    fprintf('✅ 符合实际机械结构约束\n');
else
    fprintf('⚠️  存在物理约束违反\n');
    if negative_pos_change > 0
        fprintf('❌ 位置回退\n');
    end
    if negative_vel_count > 0
        fprintf('❌ 负速度\n');
    end
    if max_pos > stroke + 1e-6
        fprintf('❌ 位置超调\n');
    end
    if max_vel_error > 0.1
        fprintf('❌ 速度位置不一致\n');
    end
end

fprintf('================================\n');

%% 包含必要的函数
function [time, pos, vel] = generate_ultra_precise_s_curve(dist, v_max, a_accel, a_decel, j_max, Ts)
[time, pos, vel, ~] = generate_corrected_s_curve_internal(dist, v_max, a_accel, a_decel, j_max, Ts);
end

function [time_vec, pos_vec, vel_vec, acc_vec] = generate_corrected_s_curve_internal(s_target, v_max, a_accel, a_decel, j_max, Ts)
% 参数验证
if s_target <= 0 || v_max <= 0 || a_accel <= 0 || a_decel <= 0 || j_max <= 0
    error('所有参数必须为正数');
end

% Step 1: 计算时间参数
t_j1 = a_accel / j_max;
v_j1 = 0.5 * a_accel * t_j1;
t_j2 = a_decel / j_max;
v_j2 = 0.5 * a_decel * t_j2;

% 检查是否能达到最大速度
if v_j1 + v_j2 <= v_max
    t_a = (v_max - v_j1) / a_accel;
    t_d = (v_max - v_j2) / a_decel;
    v_reach = v_max;
else
    v_reach = sqrt(s_target * j_max / 2);
    if v_reach > v_max
        v_reach = v_max;
        t_a = (v_max - v_j1) / a_accel;
        t_d = (v_max - v_j2) / a_decel;
    else
        t_j1 = sqrt(v_reach / j_max);
        t_j2 = t_j1;
        a_accel = j_max * t_j1;
        a_decel = a_accel;
        t_a = 0;
        t_d = 0;
        v_j1 = 0.5 * a_accel * t_j1;
        v_j2 = v_j1;
    end
end

% Step 2: 计算距离
s_j1 = (1/6) * j_max * t_j1^3;
s_a = v_j1 * t_a + 0.5 * a_accel * t_a^2;
s_accel_total = 2 * s_j1 + s_a;

s_j2 = (1/6) * j_max * t_j2^3;
s_d = v_j2 * t_d + 0.5 * a_decel * t_d^2;
s_decel_total = 2 * s_j2 + s_d;

s_const = s_target - s_accel_total - s_decel_total;
t_v = max(0, s_const / v_reach);

% Step 3: 计算时间节点
T1 = t_j1;
T2 = T1 + t_a;
T3 = T2 + t_j1;
T4 = T3 + t_v;
T5 = T4 + t_j2;
T6 = T5 + t_d;
T7 = T6 + t_j2;

% Step 4: 生成轨迹
time_vec = (0:Ts:T7)';
N = length(time_vec);

pos_vec = zeros(N, 1);
vel_vec = zeros(N, 1);
acc_vec = zeros(N, 1);

for i = 2:N
    t = time_vec(i-1);

    if t < T1
        jerk = j_max;
    elseif t < T2
        jerk = 0;
    elseif t < T3
        jerk = -j_max;
    elseif t < T4
        jerk = 0;
    elseif t < T5
        jerk = -j_max;
    elseif t < T6
        jerk = 0;
    elseif t < T7
        jerk = j_max;
    else
        jerk = 0;
    end

    acc_vec(i) = acc_vec(i-1) + jerk * Ts;

    if acc_vec(i) > a_accel
        acc_vec(i) = a_accel;
    elseif acc_vec(i) < -a_decel
        acc_vec(i) = -a_decel;
    end

    vel_vec(i) = vel_vec(i-1) + acc_vec(i-1) * Ts;
    pos_vec(i) = pos_vec(i-1) + vel_vec(i-1) * Ts + 0.5 * acc_vec(i-1) * Ts^2;
end

vel_vec(end) = 0;
acc_vec(end) = 0;

% 物理约束的位置校正
final_error = pos_vec(end) - s_target;
if abs(final_error) > 1e-10
    fprintf('  走车轨迹位置误差: %.8f mm，进行物理约束校正\n', final_error);

    % 找到最后一个速度为正的点
    last_positive_vel_idx = N;
    for i = N:-1:1
        if vel_vec(i) > 1e-6  % 找到最后一个明显正速度点
            last_positive_vel_idx = i;
            break;
        end
    end

    % 从最后正速度点开始，确保走车只能单向运动到目标位置
    if final_error > 0
        % 如果超调了，需要提前停止
        for i = last_positive_vel_idx:N
            if pos_vec(i) >= s_target
                % 从这点开始强制停止
                pos_vec(i:end) = s_target;
                vel_vec(i:end) = 0;
                acc_vec(i:end) = 0;
                break;
            end
        end
    else
        % 如果没到位，延长轨迹或调整最后阶段
        % 保持最后的位置为目标位置，但不允许回退
        pos_vec(end) = s_target;

        % 确保位置单调递增
        for i = 2:N
            if pos_vec(i) < pos_vec(i-1)
                pos_vec(i) = pos_vec(i-1);
            end
        end

        % 重新计算速度，确保物理一致性
        for i = 2:N
            vel_vec(i) = (pos_vec(i) - pos_vec(i-1)) / Ts;
            if vel_vec(i) < 0
                vel_vec(i) = 0;
            end
        end
    end

    % 最终确保物理约束
    vel_vec(end) = 0;
    acc_vec(end) = 0;

    % 验证修正结果
    final_error_corrected = pos_vec(end) - s_target;
    fprintf('  校正后位置误差: %.8f mm\n', final_error_corrected);

    % 检查是否有负速度
    negative_vel_count = sum(vel_vec < -1e-10);
    if negative_vel_count > 0
        fprintf('  警告：检测到 %d 个负速度点，强制修正\n', negative_vel_count);
        vel_vec = max(vel_vec, 0);
    end
end

end
