%% 走架细纱机牵伸控制算法 - 机械安全版
% 严格遵守机械约束：位置不超调、速度连续、符合物理规律

clear; clc; close all;

fprintf('=== 走架细纱机牵伸控制算法 - 机械安全版 ===\n');
fprintf('核心原则：严格遵守机械约束条件\n\n');

%% 工艺参数配置
stroke = 4000.0;           % 走车行程 (mm) - 绝对不能超过
max_speed = 600.0;         % 走车最大速度 (mm/s)
accel_pos = 300.0;         % 走车正向加速度 (mm/s²)
accel_neg = 800.0;         % 走车负向加速度 (mm/s²)
jerk = 600.0;              % 走车加加速度 (mm/s³)

luola_accel = 2000.0;      % 罗拉刹车加速度 (mm/s²)
luola_jerk = 12500.0;      % 罗拉刹车加加速度 (mm/s³)

ratio_distributed = 1.2;   % 分散牵伸比
ratio_total = 1.5;         % 总牵伸比

Ts = 0.004;                % 采样时间
tolerance = 0.01;          % 精度容差

fprintf('参数配置:\n');
fprintf('  走车行程: %.0f mm (绝对限制)\n', stroke);
fprintf('  分散牵伸比: %.1f\n', ratio_distributed);
fprintf('  总牵伸比: %.1f\n', ratio_total);

%% 第一步：生成机械安全的走车S曲线
fprintf('\n第一步：生成机械安全的走车S曲线...\n');
tic;

[master_time, master_pos, master_vel] = generate_mechanically_safe_s_curve(...
    stroke, max_speed, accel_pos, accel_neg, jerk, Ts);

% 🚨 机械安全验证
max_position = max(master_pos);
if max_position > stroke + tolerance
    error('❌ 机械安全违反：走车位置超过限制 %.3f mm', max_position - stroke);
end

fprintf('  ✅ 走车轨迹生成完成 (耗时: %.3fs)\n', toc);
fprintf('  数据点数: %d\n', length(master_time));
fprintf('  最大位置: %.6f mm (限制: %.0f mm)\n', max_position, stroke);
fprintf('  最终位置: %.6f mm\n', master_pos(end));
fprintf('  总时间: %.3f s\n', master_time(end));

%% 第二步：计算理想同步轨迹
fprintf('\n第二步：计算理想同步轨迹...\n');
tic;

% 🚨 关键机械约束：罗拉位置绝对限制
max_slave_pos = stroke / ratio_total;  % 罗拉绝对不能超过这个位置

% 理想同步：罗拉 = 走车 / 分散牵伸比
ideal_slave_pos = master_pos / ratio_distributed;
ideal_slave_vel = master_vel / ratio_distributed;

% 🔧 机械安全检查：确保罗拉位置不超限
slave_violation_idx = find(ideal_slave_pos > max_slave_pos, 1, 'first');
if ~isempty(slave_violation_idx)
    fprintf('  ⚠️  检测到罗拉位置超限，在时刻 %.3f s\n', master_time(slave_violation_idx));
    fprintf('  超限位置: %.3f mm (限制: %.3f mm)\n', ...
            ideal_slave_pos(slave_violation_idx), max_slave_pos);

    % 截断理想轨迹到安全范围
    ideal_slave_pos(slave_violation_idx:end) = max_slave_pos;
    ideal_slave_vel(slave_violation_idx:end) = 0;  % 超限后速度为0
end

% 最终目标位置
target_pos = max_slave_pos;

fprintf('  ✅ 理想同步轨迹计算完成 (耗时: %.3fs)\n', toc);
fprintf('  罗拉目标位置: %.3f mm\n', target_pos);
fprintf('  罗拉位置限制: %.3f mm\n', max_slave_pos);
fprintf('  如果全程同步，罗拉最终位置: %.3f mm\n', ideal_slave_pos(end));
if ~isempty(slave_violation_idx)
    fprintf('  ⚠️  理想同步轨迹已截断以满足机械约束\n');
end

%% 第三步：机械安全的拐点搜索
fprintf('\n第三步：机械安全的拐点搜索...\n');
tic;

% 🎯 搜索策略：从合理区间开始搜索
N = length(master_time);
search_start = round(0.4 * N);  % 从40%开始
search_end = round(0.9 * N);    % 到90%结束

best_turning_point = [];
min_error = inf;
search_count = 0;

fprintf('  搜索区间: %.1f%% - %.1f%% 的轨迹\n', 40, 90);

for i = search_end:-1:search_start
    search_count = search_count + 1;

    current_pos = ideal_slave_pos(i);
    current_vel = ideal_slave_vel(i);
    current_time = master_time(i);

    % 🚨 机械约束检查：罗拉位置不能超限
    if current_pos > max_slave_pos
        continue;  % 跳过超限的拐点
    end

    % 计算刹车距离
    brake_distance = calculate_safe_braking_distance(current_vel, luola_accel, luola_jerk);

    % 期望停止位置
    expected_stop_pos = current_pos + brake_distance;

    % 🚨 确保停止位置也不超限
    if expected_stop_pos > max_slave_pos
        continue;  % 跳过会导致超限的拐点
    end

    % 计算误差
    position_error = abs(expected_stop_pos - target_pos);

    % 记录最优解
    if position_error < min_error
        min_error = position_error;
        best_turning_point = struct(...
            'index', i, ...
            'position', current_pos, ...
            'velocity', current_vel, ...
            'time', current_time, ...
            'brake_distance', brake_distance, ...
            'expected_stop', expected_stop_pos, ...
            'error', position_error);
    end

    % 如果误差足够小，提前结束
    if position_error < tolerance
        break;
    end
end

search_time = toc;
fprintf('  ✅ 拐点搜索完成 (耗时: %.3fs)\n', search_time);
fprintf('  搜索迭代次数: %d\n', search_count);

if isempty(best_turning_point)
    error('❌ 未找到合适的拐点');
end

fprintf('  🎯 最优拐点:\n');
fprintf('    位置: %.3f mm\n', best_turning_point.position);
fprintf('    时刻: %.3f s\n', best_turning_point.time);
fprintf('    速度: %.3f mm/s\n', best_turning_point.velocity);
fprintf('    误差: %.6f mm\n', best_turning_point.error);

%% 第四步：生成机械安全的减速轨迹
fprintf('\n第四步：生成机械安全的减速轨迹...\n');
tic;

[decel_time, decel_pos, decel_vel] = generate_safe_decel_trajectory(...
    best_turning_point.velocity, best_turning_point.position, ...
    target_pos, luola_accel, luola_jerk, Ts);

% 调整时间基准
decel_time_absolute = decel_time + best_turning_point.time;

fprintf('  ✅ 减速轨迹生成完成 (耗时: %.3fs)\n', toc);
fprintf('  减速时间: %.3f s\n', decel_time(end));
fprintf('  最终位置: %.6f mm\n', decel_pos(end));
fprintf('  位置误差: %.6f mm\n', abs(decel_pos(end) - target_pos));

%% 第五步：机械安全验证
fprintf('\n第五步：机械安全验证...\n');

% 验证牵伸比
actual_total_ratio = stroke / decel_pos(end);
ratio_error = abs(actual_total_ratio - ratio_total);
ratio_error_percent = ratio_error / ratio_total * 100;

% 验证位置限制
master_position_violation = max_position > stroke;
final_position_error = abs(decel_pos(end) - target_pos);

% 🚨 验证罗拉位置限制
max_slave_position = max([ideal_slave_pos; decel_pos]);
slave_position_violation = max_slave_position > max_slave_pos;

% 验证速度连续性
turning_point_idx = best_turning_point.index;
vel_jump_at_turning = abs(ideal_slave_vel(turning_point_idx) - decel_vel(1));

fprintf('  机械安全检查:\n');
fprintf('    走车位置超调: %s (最大: %.3f mm)\n', yesno(~master_position_violation), max_position);
fprintf('    罗拉位置超限: %s (最大: %.3f mm, 限制: %.3f mm)\n', ...
        yesno(~slave_position_violation), max_slave_position, max_slave_pos);
fprintf('    速度连续性: %s (跳变: %.3f mm/s)\n', yesno(vel_jump_at_turning < 1.0), vel_jump_at_turning);
fprintf('    牵伸比精度: %.4f%% (误差)\n', ratio_error_percent);
fprintf('    位置精度: %.6f mm (误差)\n', final_position_error);

% 总体验收
mechanical_safety = ~master_position_violation && ~slave_position_violation && vel_jump_at_turning < 1.0;
precision_ok = ratio_error_percent < 1.0 && final_position_error < 1.0;
overall_pass = mechanical_safety && precision_ok;

fprintf('\n  验收结果:\n');
fprintf('    机械安全: %s\n', passfail(mechanical_safety));
fprintf('    精度要求: %s\n', passfail(precision_ok));
fprintf('    总体验收: %s\n', passfail(overall_pass));

%% 第六步：生成验证图表
fprintf('\n第六步：生成验证图表...\n');

figure('Name', '机械安全验证结果', 'Position', [100, 100, 1400, 900]);

% 子图1: 位置轨迹（重点检查超调）
subplot(2,4,1);
plot(master_time, master_pos, 'b-', 'LineWidth', 2, 'DisplayName', '走车');
hold on;
plot(master_time, ideal_slave_pos, 'g--', 'LineWidth', 1.5, 'DisplayName', '理想同步');
if length(decel_time_absolute) > 1
    plot(decel_time_absolute, decel_pos, 'r-', 'LineWidth', 2, 'DisplayName', '独立减速');
end
plot(best_turning_point.time, best_turning_point.position, 'ko', ...
     'MarkerSize', 8, 'MarkerFaceColor', 'yellow', 'DisplayName', '拐点');
% 添加安全限制线
yline(stroke, 'k--', 'LineWidth', 2, 'DisplayName', '走车位置限制');
yline(max_slave_pos, 'r--', 'LineWidth', 2, 'DisplayName', '罗拉位置限制');
xlabel('时间 (s)');
ylabel('位置 (mm)');
title('位置轨迹对比');
legend('Location', 'best');
grid on;
ylim([0, stroke * 1.1]);

% 子图2: 速度轨迹（重点检查连续性）
subplot(2,4,2);
plot(master_time, master_vel, 'b-', 'LineWidth', 2, 'DisplayName', '走车');
hold on;
plot(master_time, ideal_slave_vel, 'g--', 'LineWidth', 1.5, 'DisplayName', '理想同步');
if length(decel_time_absolute) > 1
    plot(decel_time_absolute, decel_vel, 'r-', 'LineWidth', 2, 'DisplayName', '独立减速');
end
plot(best_turning_point.time, best_turning_point.velocity, 'ko', ...
     'MarkerSize', 8, 'MarkerFaceColor', 'yellow', 'DisplayName', '拐点');
xlabel('时间 (s)');
ylabel('速度 (mm/s)');
title('速度轨迹对比');
legend('Location', 'best');
grid on;

% 子图3: 牵伸比变化
subplot(2,4,3);
sync_ratio = ones(turning_point_idx, 1) * ratio_distributed;
plot(master_time(1:turning_point_idx), sync_ratio, 'g-', 'LineWidth', 2, 'DisplayName', '分散牵伸比');
hold on;
if length(decel_time_absolute) > 1
    total_ratio_line = ones(size(decel_time_absolute)) * actual_total_ratio;
    plot(decel_time_absolute, total_ratio_line, 'r-', 'LineWidth', 2, 'DisplayName', '总牵伸比');
end
xlabel('时间 (s)');
ylabel('牵伸比');
title('牵伸比变化');
legend('Location', 'best');
grid on;

% 子图4: 机械安全指标
subplot(2,4,4);
safety_data = [max_position/stroke, vel_jump_at_turning/10, ratio_error_percent/100];
safety_labels = {'位置比例', '速度跳变/10', '牵伸比误差%'};
bar(safety_data, 'FaceColor', [0.3, 0.7, 0.3]);
set(gca, 'XTickLabel', safety_labels);
ylabel('安全指标');
title('机械安全指标');
grid on;
ylim([0, 1.2]);

% 子图5: 误差分析
subplot(2,4,5);
error_data = [best_turning_point.error, final_position_error, ratio_error];
error_labels = {'拐点误差', '位置误差', '牵伸比误差'};
bar(error_data, 'FaceColor', [0.9, 0.4, 0.4]);
set(gca, 'XTickLabel', error_labels);
ylabel('误差值');
title('精度分析');
grid on;

% 子图6: 控制指令时序
subplot(2,4,6);
gear_time = [0, best_turning_point.time];
gear_signal = [1, 1];
pos_time = [best_turning_point.time, decel_time_absolute(end)];
pos_signal = [0, 0];

plot(gear_time, gear_signal, 'g-', 'LineWidth', 4, 'DisplayName', 'GEAR指令');
hold on;
plot(pos_time, pos_signal, 'r-', 'LineWidth', 4, 'DisplayName', 'POS指令');
xlabel('时间 (s)');
ylabel('指令状态');
title('控制指令时序');
legend('Location', 'best');
grid on;
ylim([-0.5, 1.5]);

% 子图7: 拐点详细分析
subplot(2,4,7);
if turning_point_idx > 10 && turning_point_idx < length(master_time) - 10
    plot(master_time(turning_point_idx-10:turning_point_idx+10), ...
         ideal_slave_vel(turning_point_idx-10:turning_point_idx+10), 'g-', 'LineWidth', 2);
    hold on;
end
if length(decel_time_absolute) > 1
    plot_range = min(20, length(decel_time_absolute));
    plot(decel_time_absolute(1:plot_range), decel_vel(1:plot_range), 'r-', 'LineWidth', 2);
    hold on;
end
plot(best_turning_point.time, best_turning_point.velocity, 'ko', ...
     'MarkerSize', 10, 'MarkerFaceColor', 'yellow');
xlabel('时间 (s)');
ylabel('速度 (mm/s)');
title('拐点切换详细');
grid on;

% 子图8: 总体评分
subplot(2,4,8);
score_data = [mechanical_safety*100, precision_ok*100, overall_pass*100];
score_labels = {'机械安全', '精度要求', '总体评分'};
bar(score_data, 'FaceColor', [0.2, 0.6, 0.8]);
set(gca, 'XTickLabel', score_labels);
ylabel('评分 (%)');
title('综合评分');
grid on;
ylim([0, 120]);

fprintf('  ✅ 验证图表生成完成\n');

%% 总结
fprintf('\n=== 机械安全算法总结 ===\n');
if overall_pass
    fprintf('✅ 算法验收通过\n');
    fprintf('🎯 关键结果:\n');
    fprintf('  拐点位置: %.3f mm\n', best_turning_point.position);
    fprintf('  拐点时刻: %.3f s\n', best_turning_point.time);
    fprintf('  牵伸比精度: %.4f%%\n', ratio_error_percent);
    fprintf('  位置精度: %.3f mm\n', final_position_error);
    fprintf('  机械安全: 完全合规\n');
else
    fprintf('❌ 算法需要进一步优化\n');
    if ~mechanical_safety
        fprintf('  ⚠️  机械安全问题需要解决\n');
    end
    if ~precision_ok
        fprintf('  ⚠️  精度要求需要改善\n');
    end
end
fprintf('================================\n');

%% 辅助函数定义

function [time_vec, pos_vec, vel_vec] = generate_mechanically_safe_s_curve(s_target, v_max, a_accel, a_decel, j_max, Ts)
%% 生成机械安全的S曲线轨迹

% 🎯 使用标准S曲线公式，确保不超调
t_j1 = a_accel / j_max;
t_j2 = a_decel / j_max;

% 检查轮廓类型
v_max_triangle = sqrt(2 * s_target * a_accel * a_decel / (a_accel + a_decel));

if v_max_triangle <= v_max
    % 三角形轮廓
    v_reach = v_max_triangle;
    t_j1 = sqrt(v_reach / j_max);
    t_j2 = sqrt(v_reach / j_max);
    t_a = 0;
    t_d = 0;
else
    % 梯形轮廓
    v_reach = v_max;
    t_a = (v_reach - a_accel * t_j1) / a_accel;
    t_d = (v_reach - a_decel * t_j2) / a_decel;
    t_a = max(0, t_a);
    t_d = max(0, t_d);
end

% 计算时间节点
T1 = t_j1;
T2 = T1 + t_a;
T3 = T2 + t_j1;
T4 = T3 + (s_target - v_reach * (t_j1 + t_a + t_j2)) / v_reach;
T5 = T4 + t_j2;
T6 = T5 + t_d;
T7 = T6 + t_j2;

% 生成时间序列
time_vec = (0:Ts:T7)';
N = length(time_vec);
pos_vec = zeros(N, 1);
vel_vec = zeros(N, 1);
acc_vec = zeros(N, 1);

% 🔧 精确的数值积分
for i = 2:N
    t = time_vec(i-1);

    % 确定当前段的加加速度
    if t < T1
        jerk = j_max;
    elseif t < T2
        jerk = 0;
    elseif t < T3
        jerk = -j_max;
    elseif t < T4
        jerk = 0;
    elseif t < T5
        jerk = -j_max;
    elseif t < T6
        jerk = 0;
    elseif t < T7
        jerk = j_max;
    else
        jerk = 0;
    end

    % 数值积分
    acc_vec(i) = acc_vec(i-1) + jerk * Ts;
    vel_vec(i) = vel_vec(i-1) + acc_vec(i-1) * Ts;
    vel_vec(i) = max(vel_vec(i), 0);

    pos_vec(i) = pos_vec(i-1) + vel_vec(i-1) * Ts + 0.5 * acc_vec(i-1) * Ts^2;

    % 🚨 机械安全保护：绝对不允许超调
    if pos_vec(i) >= s_target
        pos_vec(i) = s_target;
        vel_vec(i) = 0;
        acc_vec(i) = 0;
        % 确保后续所有点都停在目标位置
        if i < N
            pos_vec((i+1):end) = s_target;
            vel_vec((i+1):end) = 0;
            acc_vec((i+1):end) = 0;
        end
        break;
    end
end

% 最终确保精确停止
pos_vec(end) = s_target;
vel_vec(end) = 0;

end

function brake_distance = calculate_safe_braking_distance(v0, a_decel, j_max)
%% 计算安全刹车距离

if v0 <= 0
    brake_distance = 0;
    return;
end

t_j = a_decel / j_max;
if v0 * j_max < a_decel^2
    % 纯S形减速
    t_j = sqrt(v0 / j_max);
    brake_distance = (2/3) * v0 * t_j;
else
    % 梯形减速
    brake_distance = v0^2 / (2 * a_decel) + (a_decel * t_j^2) / 6;
end

end

function [time_vec, pos_vec, vel_vec] = generate_safe_decel_trajectory(v0, pos0, target_pos, a_decel, j_max, Ts)
%% 生成安全的减速轨迹

if v0 <= 0
    time_vec = 0;
    pos_vec = pos0;
    vel_vec = 0;
    return;
end

% 计算减速参数
t_j = a_decel / j_max;
if v0 * j_max < a_decel^2
    t_j = sqrt(v0 / j_max);
    t_const = 0;
    T_total = 2 * t_j;
else
    t_const = v0 / a_decel - t_j;
    T_total = 2 * t_j + t_const;
end

% 时间节点
T1 = t_j;
T2 = T1 + t_const;
T3 = T2 + t_j;

% 生成轨迹
time_vec = (0:Ts:T_total)';
N = length(time_vec);
pos_vec = zeros(N, 1);
vel_vec = zeros(N, 1);
acc_vec = zeros(N, 1);
pos_vec(1) = pos0;
vel_vec(1) = v0;

for i = 2:N
    t = time_vec(i-1);

    if t < T1
        jerk = -j_max;
    elseif t < T2
        jerk = 0;
    elseif t < T3
        jerk = j_max;
    else
        jerk = 0;
    end

    % 数值积分
    acc_vec(i) = acc_vec(i-1) + jerk * Ts;
    acc_vec(i) = max(acc_vec(i), -a_decel);

    vel_vec(i) = vel_vec(i-1) + acc_vec(i-1) * Ts;
    vel_vec(i) = max(vel_vec(i), 0);

    pos_vec(i) = pos_vec(i-1) + vel_vec(i-1) * Ts + 0.5 * acc_vec(i-1) * Ts^2;

    % 当速度接近0时自然停止
    if vel_vec(i) < 0.1
        vel_vec(i) = 0;
        break;
    end
end

% 调整到精确的目标位置
final_error = pos_vec(end) - target_pos;
if abs(final_error) > 0.1
    % 线性调整所有位置
    for i = 1:length(pos_vec)
        ratio = (i - 1) / (length(pos_vec) - 1);
        pos_vec(i) = pos_vec(i) - final_error * ratio;
    end
end

pos_vec(end) = target_pos;
vel_vec(end) = 0;

end

function result = yesno(condition)
%% 是否判断辅助函数
if condition
    result = '是';
else
    result = '否';
end
end

function result = passfail(condition)
%% 通过失败判断辅助函数
if condition
    result = '通过';
else
    result = '失败';
end
end
