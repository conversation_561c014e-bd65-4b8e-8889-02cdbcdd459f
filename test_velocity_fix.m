%% 测试速度曲线修复效果
clear; clc;

fprintf('=== 测试速度曲线修复效果 ===\n');

%% 参数设置
stroke = 4000.0;
max_speed = 600.0;
accel_pos = 300.0;
accel_neg = 800.0;
jerk = 600.0;
luola_accel = 2000.0;
luola_jerk = 12500.0;
ratio_distributed = 1.2;
ratio_total = 1.5;
Ts = 0.004;

%% 生成走车轨迹
[master_time, master_pos, master_vel] = generate_ultra_precise_s_curve(stroke, max_speed, accel_pos, accel_neg, jerk, Ts);

%% 计算理想同步轨迹
ideal_slave_pos = master_pos / ratio_distributed;
ideal_slave_vel = master_vel / ratio_distributed;
target_pos = stroke / ratio_total;

%% 找拐点
[turning_point_index, turning_point_time, turning_point_pos, turning_point_vel, ~] = ...
    find_ultra_precise_turning_point(master_time, ideal_slave_pos, ideal_slave_vel, target_pos, luola_accel, luola_jerk, Ts);

%% 生成减速轨迹
[decel_time_rel, decel_vel] = generate_ultra_precise_decel(turning_point_vel, luola_accel, luola_jerk, Ts);

fprintf('减速轨迹分析:\n');
fprintf('  起始速度: %.3f mm/s\n', decel_vel(1));
fprintf('  最终速度: %.3f mm/s\n', decel_vel(end));
fprintf('  最小速度: %.3f mm/s\n', min(decel_vel));
fprintf('  速度点数: %d\n', length(decel_vel));

% 检查速度连续性
vel_diff = diff(decel_vel);
max_vel_jump = max(abs(vel_diff));
fprintf('  最大速度跳跃: %.6f mm/s\n', max_vel_jump);

% 检查是否有负速度
negative_count = sum(decel_vel < 0);
fprintf('  负速度点数: %d\n', negative_count);

% 检查速度单调性
non_monotonic = sum(vel_diff > 0.001);  % 允许小的数值误差
fprintf('  非单调递减点数: %d\n', non_monotonic);

%% 应用平滑处理
decel_vel_smooth = smooth_velocity_profile(decel_vel, Ts);

fprintf('\n平滑后分析:\n');
fprintf('  起始速度: %.3f mm/s\n', decel_vel_smooth(1));
fprintf('  最终速度: %.3f mm/s\n', decel_vel_smooth(end));
fprintf('  最小速度: %.3f mm/s\n', min(decel_vel_smooth));

vel_diff_smooth = diff(decel_vel_smooth);
max_vel_jump_smooth = max(abs(vel_diff_smooth));
fprintf('  最大速度跳跃: %.6f mm/s\n', max_vel_jump_smooth);

negative_count_smooth = sum(decel_vel_smooth < 0);
fprintf('  负速度点数: %d\n', negative_count_smooth);

non_monotonic_smooth = sum(vel_diff_smooth > 0.001);
fprintf('  非单调递减点数: %d\n', non_monotonic_smooth);

%% 输出关键数据点用于分析
fprintf('\n关键数据点:\n');
n = length(decel_vel);
indices = [1, round(n*0.25), round(n*0.5), round(n*0.75), n];
for i = indices
    if i <= n
        fprintf('  点%d: 时间=%.3fs, 原始速度=%.3f, 平滑速度=%.3f\n', ...
            i, decel_time_rel(i), decel_vel(i), decel_vel_smooth(i));
    end
end

fprintf('\n=== 测试完成 ===\n');

%% 包含必要的函数
function [time, pos, vel] = generate_ultra_precise_s_curve(dist, v_max, a_accel, a_decel, j_max, Ts)
[time, pos, vel, ~] = generate_corrected_s_curve_internal(dist, v_max, a_accel, a_decel, j_max, Ts);
end

function [time_vec, pos_vec, vel_vec, acc_vec] = generate_corrected_s_curve_internal(s_target, v_max, a_accel, a_decel, j_max, Ts)
% 简化版S曲线生成器
t_j1 = a_accel / j_max;
v_j1 = 0.5 * a_accel * t_j1;
t_j2 = a_decel / j_max;
v_j2 = 0.5 * a_decel * t_j2;

if v_j1 + v_j2 <= v_max
    t_a = (v_max - v_j1) / a_accel;
    t_d = (v_max - v_j2) / a_decel;
    v_reach = v_max;
else
    v_reach = sqrt(s_target * j_max / 2);
    if v_reach > v_max
        v_reach = v_max;
        t_a = (v_max - v_j1) / a_accel;
        t_d = (v_max - v_j2) / a_decel;
    else
        t_j1 = sqrt(v_reach / j_max);
        t_j2 = t_j1;
        a_accel = j_max * t_j1;
        a_decel = a_accel;
        t_a = 0;
        t_d = 0;
        v_j1 = 0.5 * a_accel * t_j1;
        v_j2 = v_j1;
    end
end

s_j1 = (1/6) * j_max * t_j1^3;
s_a = v_j1 * t_a + 0.5 * a_accel * t_a^2;
s_accel_total = 2 * s_j1 + s_a;
s_j2 = (1/6) * j_max * t_j2^3;
s_d = v_j2 * t_d + 0.5 * a_decel * t_d^2;
s_decel_total = 2 * s_j2 + s_d;
s_const = s_target - s_accel_total - s_decel_total;
t_v = max(0, s_const / v_reach);

T1 = t_j1;
T2 = T1 + t_a;
T3 = T2 + t_j1;
T4 = T3 + t_v;
T5 = T4 + t_j2;
T6 = T5 + t_d;
T7 = T6 + t_j2;

time_vec = (0:Ts:T7)';
N = length(time_vec);
pos_vec = zeros(N, 1);
vel_vec = zeros(N, 1);
acc_vec = zeros(N, 1);

for i = 2:N
    t = time_vec(i-1);
    if t < T1
        jerk = j_max;
    elseif t < T2
        jerk = 0;
    elseif t < T3
        jerk = -j_max;
    elseif t < T4
        jerk = 0;
    elseif t < T5
        jerk = -j_max;
    elseif t < T6
        jerk = 0;
    elseif t < T7
        jerk = j_max;
    else
        jerk = 0;
    end
    
    acc_vec(i) = acc_vec(i-1) + jerk * Ts;
    if acc_vec(i) > a_accel
        acc_vec(i) = a_accel;
    elseif acc_vec(i) < -a_decel
        acc_vec(i) = -a_decel;
    end
    
    vel_vec(i) = vel_vec(i-1) + acc_vec(i-1) * Ts;
    pos_vec(i) = pos_vec(i-1) + vel_vec(i-1) * Ts + 0.5 * acc_vec(i-1) * Ts^2;
end

vel_vec(end) = 0;
acc_vec(end) = 0;

final_error = pos_vec(end) - s_target;
if abs(final_error) > 1e-10
    correction_start = max(1, round(0.9 * N));
    for i = correction_start:N
        ratio = (i - correction_start + 1) / (N - correction_start + 1);
        pos_vec(i) = pos_vec(i) - final_error * ratio;
    end
end
end

function [turning_point_index, turning_point_time, turning_point_pos, turning_point_vel, turning_point_error] = ...
    find_ultra_precise_turning_point(master_time, ideal_slave_pos, ideal_slave_vel, target_pos, luola_accel, luola_jerk, ~)
N = length(master_time);
best_error = inf;
best_index = 1;
search_start = max(1, round(0.3 * N));
search_end = min(N, round(0.9 * N));

for i = search_end:-1:search_start
    current_pos = ideal_slave_pos(i);
    current_vel = ideal_slave_vel(i);
    if current_vel <= 0
        continue;
    end
    brake_distance = calculate_ultra_precise_brake_distance(current_vel, luola_accel, luola_jerk);
    expected_stop = current_pos + brake_distance;
    error_val = abs(expected_stop - target_pos);
    if error_val < best_error
        best_error = error_val;
        best_index = i;
    end
    if error_val < 1e-6
        break;
    end
end

if ideal_slave_vel(best_index) <= 0
    for i = best_index:search_end
        if ideal_slave_vel(i) > 0
            best_index = i;
            break;
        end
    end
end

turning_point_index = best_index;
turning_point_time = master_time(best_index);
turning_point_pos = ideal_slave_pos(best_index);
turning_point_vel = ideal_slave_vel(best_index);
brake_distance = calculate_ultra_precise_brake_distance(turning_point_vel, luola_accel, luola_jerk);
expected_stop = turning_point_pos + brake_distance;
turning_point_error = abs(expected_stop - target_pos);
end

function brake_dist = calculate_ultra_precise_brake_distance(v0, a_decel, j_max)
if v0 <= 0
    brake_dist = 0;
    return;
end
t_j = a_decel / j_max;
if v0 * j_max < a_decel^2
    t_j = sqrt(v0 / j_max);
    brake_dist = (2/3) * v0 * t_j;
else
    brake_dist = v0^2 / (2 * a_decel) + (a_decel * t_j^2) / 6;
end
end

function [time, vel] = generate_ultra_precise_decel(v0, a_decel, j_max, Ts)
if v0 <= 0
    time = 0;
    vel = 0;
    return;
end

t_j = a_decel / j_max;
if v0 * j_max < a_decel^2
    t_j = sqrt(v0 / j_max);
    a_actual = j_max * t_j;
    t_const = 0;
    T_total = 2 * t_j;
else
    a_actual = a_decel;
    t_const = v0 / a_actual - t_j;
    T_total = 2 * t_j + t_const;
end

T1 = t_j;
T2 = T1 + t_const;
T3 = T2 + t_j;

time = (0:Ts:T_total)';
N = length(time);
if abs(time(end) - T_total) > Ts/2
    time = [time; T_total];
    N = N + 1;
end

vel = zeros(N, 1);
acc = zeros(N, 1);
vel(1) = v0;

for i = 2:N
    t = time(i-1);
    if t < T1
        jerk_val = -j_max;
    elseif t < T2
        jerk_val = 0;
    elseif t < T3
        jerk_val = j_max;
    else
        jerk_val = 0;
    end

    acc(i) = acc(i-1) + jerk_val * Ts;
    if acc(i) < -a_actual
        acc(i) = -a_actual;
    elseif acc(i) > 0
        acc(i) = 0;
    end

    vel(i) = vel(i-1) + acc(i-1) * Ts;
    if vel(i) < 0
        vel(i) = 0;
        for j = i+1:N
            vel(j) = 0;
            acc(j) = 0;
        end
        break;
    end
end

vel(end) = 0;
end

function vel_smooth = smooth_velocity_profile(vel, ~)
if length(vel) < 3
    vel_smooth = vel;
    return;
end

vel_smooth = vel;
N = length(vel);
window_size = min(5, floor(N/10));
if window_size >= 3
    for i = window_size:N-window_size+1
        start_idx = i - floor(window_size/2);
        end_idx = i + floor(window_size/2);
        vel_smooth(i) = mean(vel(start_idx:end_idx));
    end
end

vel_smooth(1) = vel(1);
vel_smooth(end) = vel(end);

for i = 2:N
    if vel_smooth(i) > vel_smooth(i-1)
        vel_smooth(i) = vel_smooth(i-1);
    end
end

vel_smooth = max(vel_smooth, 0);
end
