function [time_vec, vel_vec] = generate_golden_standard_decel_profile(v0, a_decel, j_max, Ts)
%% "黄金标准"基准模块3：减速轨迹生成器
% 生成平滑的S曲线减速轨迹
% 确保罗拉独立减速过程的平稳性，支持不同加速度和加加速度参数

if v0 <= 0.001
    time_vec = 0;
    vel_vec = 0;
    return;
end

% 精确计算减速参数
t_j = a_decel / j_max;
if v0 * j_max < a_decel^2
    % 三角形减速轮廓
    t_j = sqrt(v0 / j_max);
    a_actual = j_max * t_j;
    t_const = 0;
    T_total = 2 * t_j;
else
    % 梯形减速轮廓
    a_actual = a_decel;
    t_const = v0 / a_actual - t_j;
    T_total = 2 * t_j + t_const;
end

% 时间节点
T1 = t_j;
T2 = T1 + t_const;
T3 = T2 + t_j;

% 生成精确时间序列
time_vec = (0:Ts:T_total)';
N = length(time_vec);

% 确保最后一个时间点接近总时间
if abs(time_vec(end) - T_total) > Ts/2
    time_vec = [time_vec; T_total];
    N = N + 1;
end

vel_vec = zeros(N, 1);
acc_vec = zeros(N, 1);
vel_vec(1) = v0;

% 精确生成减速轨迹
for i = 2:N
    t = time_vec(i-1);
    
    if t < T1
        jerk = -j_max;
    elseif t < T2
        jerk = 0;
    elseif t < T3
        jerk = j_max;
    else
        jerk = 0;
    end
    
    acc_vec(i) = acc_vec(i-1) + jerk * Ts;
    acc_vec(i) = max(acc_vec(i), -a_actual);
    acc_vec(i) = min(acc_vec(i), 0);

    vel_vec(i) = vel_vec(i-1) + acc_vec(i-1) * Ts;
    vel_vec(i) = max(vel_vec(i), 0);

    % 平滑结束：当速度接近0时自然停止
    if vel_vec(i) < 0.1
        vel_vec(i) = 0;
        % 确保后续所有点都是0
        if i < N
            vel_vec((i+1):end) = 0;
        end
        break;
    end
end

% 确保最终速度为0，但避免突然跳变
if vel_vec(end) > 0.1
    % 如果最后速度还不是0，进行平滑过渡
    final_points = min(5, length(vel_vec));
    for i = (length(vel_vec) - final_points + 1):length(vel_vec)
        ratio = (i - (length(vel_vec) - final_points)) / final_points;
        vel_vec(i) = vel_vec(i) * (1 - ratio);
    end
end
vel_vec(end) = 0;

end
