%% 测试机械安全性修复效果
clear; clc;

fprintf('=== 测试机械安全性修复效果 ===\n');

%% 参数设置
stroke = 4000.0;
max_speed = 600.0;
accel_pos = 300.0;
accel_neg = 800.0;  % 最大允许减速度
jerk = 600.0;
Ts = 0.004;

fprintf('机械限制参数:\n');
fprintf('  最大减速度: %.0f mm/s²\n', accel_neg);
fprintf('  最大加加速度: %.0f mm/s³\n', jerk);

%% 生成走车轨迹
[master_time, master_pos, master_vel] = generate_ultra_precise_s_curve(stroke, max_speed, accel_pos, accel_neg, jerk, Ts);

%% 分析机械安全性
fprintf('\n机械安全性分析:\n');

% 计算加速度
master_acc = [0; diff(master_vel) / Ts];

% 计算加加速度
master_jerk = [0; diff(master_acc) / Ts];

% 统计分析
max_vel = max(master_vel);
max_acc = max(master_acc);
min_acc = min(master_acc);
max_decel = -min_acc;
max_jerk_pos = max(master_jerk);
min_jerk_neg = min(master_jerk);

fprintf('  最大速度: %.2f mm/s (限制: %.0f mm/s)\n', max_vel, max_speed);
fprintf('  最大加速度: %.2f mm/s² (限制: %.0f mm/s²)\n', max_acc, accel_pos);
fprintf('  最大减速度: %.2f mm/s² (限制: %.0f mm/s²)\n', max_decel, accel_neg);
fprintf('  最大正加加速度: %.2f mm/s³ (限制: %.0f mm/s³)\n', max_jerk_pos, jerk);
fprintf('  最大负加加速度: %.2f mm/s³ (限制: %.0f mm/s³)\n', -min_jerk_neg, jerk);

%% 检查危险的急停情况
fprintf('\n急停检查:\n');

% 查找速度大幅下降的点
vel_drops = -diff(master_vel);
dangerous_drops = vel_drops > 50;  % 速度下降超过50mm/s的点

if sum(dangerous_drops) > 0
    fprintf('  ❌ 检测到 %d 个危险急停点！\n', sum(dangerous_drops));
    
    % 找到最危险的几个点
    [max_drops, drop_indices] = sort(vel_drops, 'descend');
    fprintf('  最危险的急停点:\n');
    for i = 1:min(3, sum(dangerous_drops))
        idx = drop_indices(i) + 1;  % +1因为diff的索引偏移
        if idx <= length(master_time)
            fprintf('    时间 %.3fs: 速度从 %.1f 降到 %.1f mm/s (降幅 %.1f mm/s)\n', ...
                master_time(idx), master_vel(idx-1), master_vel(idx), max_drops(i));
        end
    end
else
    fprintf('  ✅ 未检测到危险急停\n');
end

%% 检查减速度是否超限
fprintf('\n减速度安全检查:\n');

excessive_decel = master_acc < -accel_neg * 1.05;  % 允许5%的误差
if sum(excessive_decel) > 0
    fprintf('  ❌ 检测到 %d 个超限减速点！\n', sum(excessive_decel));
    
    % 找到最严重的超限点
    over_limit_acc = master_acc(excessive_decel);
    over_limit_times = master_time(excessive_decel);
    [min_acc_val, min_idx] = min(over_limit_acc);
    
    fprintf('  最严重超限: 时间 %.3fs, 减速度 %.2f mm/s² (超限 %.2f mm/s²)\n', ...
        over_limit_times(min_idx), -min_acc_val, -min_acc_val - accel_neg);
else
    fprintf('  ✅ 减速度在安全范围内\n');
end

%% 检查加加速度是否超限
fprintf('\n加加速度安全检查:\n');

excessive_jerk_pos = master_jerk > jerk * 1.05;
excessive_jerk_neg = master_jerk < -jerk * 1.05;

if sum(excessive_jerk_pos) > 0 || sum(excessive_jerk_neg) > 0
    fprintf('  ❌ 检测到加加速度超限！\n');
    if sum(excessive_jerk_pos) > 0
        fprintf('    正向超限点数: %d\n', sum(excessive_jerk_pos));
    end
    if sum(excessive_jerk_neg) > 0
        fprintf('    负向超限点数: %d\n', sum(excessive_jerk_neg));
    end
else
    fprintf('  ✅ 加加速度在安全范围内\n');
end

%% 检查速度变化的平滑性
fprintf('\n速度平滑性检查:\n');

% 计算速度变化率的标准差
vel_change_rate = abs(diff(master_vel));
vel_smoothness = std(vel_change_rate);
max_vel_change = max(vel_change_rate);

fprintf('  速度变化率标准差: %.3f mm/s\n', vel_smoothness);
fprintf('  最大单步速度变化: %.3f mm/s\n', max_vel_change);

if max_vel_change > 20  % 单步变化超过20mm/s认为不平滑
    fprintf('  ❌ 速度变化不够平滑\n');
else
    fprintf('  ✅ 速度变化平滑\n');
end

%% 机械冲击评估
fprintf('\n机械冲击评估:\n');

% 计算动能变化（简化评估）
kinetic_energy = 0.5 * master_vel.^2;  % 假设单位质量
energy_change = abs(diff(kinetic_energy));
max_energy_change = max(energy_change);

fprintf('  最大动能变化: %.2f (单位质量)\n', max_energy_change);

% 评估冲击风险
if max_energy_change > 10000  % 经验阈值
    fprintf('  ⚠️  高冲击风险\n');
elseif max_energy_change > 5000
    fprintf('  ⚠️  中等冲击风险\n');
else
    fprintf('  ✅ 低冲击风险\n');
end

%% 总体安全评估
fprintf('\n=== 机械安全性总体评估 ===\n');

safety_score = 0;
total_checks = 6;

% 各项检查评分
if sum(dangerous_drops) == 0
    safety_score = safety_score + 1;
    fprintf('✅ 无危险急停\n');
else
    fprintf('❌ 存在危险急停\n');
end

if sum(excessive_decel) == 0
    safety_score = safety_score + 1;
    fprintf('✅ 减速度安全\n');
else
    fprintf('❌ 减速度超限\n');
end

if sum(excessive_jerk_pos) == 0 && sum(excessive_jerk_neg) == 0
    safety_score = safety_score + 1;
    fprintf('✅ 加加速度安全\n');
else
    fprintf('❌ 加加速度超限\n');
end

if max_vel_change <= 20
    safety_score = safety_score + 1;
    fprintf('✅ 速度变化平滑\n');
else
    fprintf('❌ 速度变化不平滑\n');
end

if max_energy_change <= 5000
    safety_score = safety_score + 1;
    fprintf('✅ 冲击风险低\n');
else
    fprintf('❌ 冲击风险高\n');
end

max_pos = max(master_pos);
if max_pos <= stroke + 1e-6
    safety_score = safety_score + 1;
    fprintf('✅ 无位置超调\n');
else
    fprintf('❌ 位置超调\n');
end

% 总体评估
safety_percentage = (safety_score / total_checks) * 100;
fprintf('\n机械安全性得分: %d/%d (%.1f%%)\n', safety_score, total_checks, safety_percentage);

if safety_percentage >= 90
    fprintf('🎉 机械安全性优秀！可以安全运行\n');
elseif safety_percentage >= 70
    fprintf('⚠️  机械安全性良好，建议进一步优化\n');
else
    fprintf('❌ 机械安全性不足，存在设备损坏风险！\n');
end

fprintf('================================\n');

%% 包含必要的函数
function [time, pos, vel] = generate_ultra_precise_s_curve(dist, v_max, a_accel, a_decel, j_max, Ts)
% 简化版S曲线生成器，用于测试
fprintf('  生成走车轨迹...\n');

% 基本参数计算
t_j1 = a_accel / j_max;
t_j2 = a_decel / j_max;
v_j1 = 0.5 * a_accel * t_j1;
v_j2 = 0.5 * a_decel * t_j2;

% 检查是否能达到最大速度
if v_j1 + v_j2 <= v_max
    t_a = (v_max - v_j1) / a_accel;
    t_d = (v_max - v_j2) / a_decel;
    v_reach = v_max;
else
    v_reach = sqrt(dist * j_max / 2);
    if v_reach > v_max
        v_reach = v_max;
        t_a = (v_max - v_j1) / a_accel;
        t_d = (v_max - v_j2) / a_decel;
    else
        t_j1 = sqrt(v_reach / j_max);
        t_j2 = t_j1;
        a_accel = j_max * t_j1;
        a_decel = a_accel;
        t_a = 0;
        t_d = 0;
        v_j1 = 0.5 * a_accel * t_j1;
        v_j2 = v_j1;
    end
end

% 计算距离和时间
s_j1 = (1/6) * j_max * t_j1^3;
s_a = v_j1 * t_a + 0.5 * a_accel * t_a^2;
s_accel_total = 2 * s_j1 + s_a;
s_j2 = (1/6) * j_max * t_j2^3;
s_d = v_j2 * t_d + 0.5 * a_decel * t_d^2;
s_decel_total = 2 * s_j2 + s_d;
s_const = dist - s_accel_total - s_decel_total;
t_v = max(0, s_const / v_reach);

% 时间节点
T1 = t_j1;
T2 = T1 + t_a;
T3 = T2 + t_j1;
T4 = T3 + t_v;
T5 = T4 + t_j2;
T6 = T5 + t_d;
T7 = T6 + t_j2;

% 生成轨迹
time = (0:Ts:T7)';
N = length(time);
pos = zeros(N, 1);
vel = zeros(N, 1);
acc = zeros(N, 1);

for i = 2:N
    t = time(i-1);
    if t < T1
        jerk = j_max;
    elseif t < T2
        jerk = 0;
    elseif t < T3
        jerk = -j_max;
    elseif t < T4
        jerk = 0;
    elseif t < T5
        jerk = -j_max;
    elseif t < T6
        jerk = 0;
    elseif t < T7
        jerk = j_max;
    else
        jerk = 0;
    end

    acc(i) = acc(i-1) + jerk * Ts;
    if acc(i) > a_accel
        acc(i) = a_accel;
    elseif acc(i) < -a_decel
        acc(i) = -a_decel;
    end

    vel(i) = vel(i-1) + acc(i-1) * Ts;
    pos(i) = pos(i-1) + vel(i-1) * Ts + 0.5 * acc(i-1) * Ts^2;
end

vel(end) = 0;

% 机械安全的位置校正
final_error = pos(end) - dist;
if abs(final_error) > 1e-6
    fprintf('  检测到位置误差 %.2f mm，进行机械安全校正\n', final_error);

    % 找到接近目标的点（在95%位置）
    target_95 = dist * 0.95;
    approach_idx = 1;
    for i = 1:N
        if pos(i) >= target_95
            approach_idx = i;
            break;
        end
    end

    if approach_idx < N
        % 从95%位置开始进行安全减速
        remaining_dist = dist - pos(approach_idx);
        current_vel = vel(approach_idx);

        if current_vel > 0 && remaining_dist > 0
            % 计算安全减速所需的距离
            safe_stop_dist = current_vel^2 / (2 * a_decel);

            if safe_stop_dist <= remaining_dist
                % 可以安全停止，生成减速轨迹
                for i = approach_idx+1:N
                    t_rel = (i - approach_idx) * Ts;
                    vel_new = max(0, current_vel - a_decel * t_rel);
                    vel(i) = vel_new;

                    if i > approach_idx+1
                        pos(i) = pos(i-1) + (vel(i-1) + vel(i)) * 0.5 * Ts;
                    end

                    if vel(i) <= 0
                        pos(i:end) = dist;
                        vel(i:end) = 0;
                        break;
                    end
                end
            else
                % 距离不足，降低减速度
                safe_decel = current_vel^2 / (2 * remaining_dist);
                safe_decel = min(safe_decel, a_decel);

                for i = approach_idx+1:N
                    t_rel = (i - approach_idx) * Ts;
                    vel_new = max(0, current_vel - safe_decel * t_rel);
                    vel(i) = vel_new;

                    if i > approach_idx+1
                        pos(i) = pos(i-1) + (vel(i-1) + vel(i)) * 0.5 * Ts;
                    end

                    if vel(i) <= 0 || pos(i) >= dist
                        pos(i:end) = dist;
                        vel(i:end) = 0;
                        break;
                    end
                end
            end
        end
    end

    % 最终确保精确到达目标
    pos(end) = dist;
    vel(end) = 0;
end

fprintf('  轨迹生成完成: 时长=%.3fs, 最终位置=%.2fmm\n', time(end), pos(end));
end
