function brake_distance = calculate_golden_standard_braking_distance(v0, a_decel, j_max, ~)
%% "黄金标准"基准模块2：刹车距离计算器
% 精确计算给定初速度下的最优刹车距离
% 支持S曲线减速轮廓，为去同步拐点求解提供关键数据

if v0 <= 0.001
    brake_distance = 0;
    return;
end

% 高精度解析公式
t_j = a_decel / j_max;

if v0 * j_max < a_decel^2
    % 三角形减速轮廓
    t_j = sqrt(v0 / j_max);
    brake_distance = (2/3) * v0 * t_j;
else
    % 梯形减速轮廓
    brake_distance = v0^2 / (2 * a_decel) + (a_decel * t_j^2) / 6;
end

end
