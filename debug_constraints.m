%% 调试约束检查
% 专门用于调试A类约束失败的原因

clear; clc;

fprintf('=== 调试约束检查 ===\n');

% 运行算法获取数据
run('optimized_spinning_machine_algorithm.m');

fprintf('\n=== 详细约束分析 ===\n');

% 重新定义约束参数
CONSTRAINT_A_MAX_MASTER_POS = 4000.0;
CONSTRAINT_A_MAX_SLAVE_POS = 2666.667;
CONSTRAINT_A_MAX_VEL_JUMP = 5.0;

% 详细检查每个A类约束
fprintf('A类约束详细检查:\n');

% A1.1 走车位置限制
max_master_pos_actual = max(master_pos);
a11_pass = max_master_pos_actual <= CONSTRAINT_A_MAX_MASTER_POS + 1e-6;
fprintf('  A1.1 走车位置限制:\n');
fprintf('    实际最大位置: %.6f mm\n', max_master_pos_actual);
fprintf('    限制值: %.6f mm\n', CONSTRAINT_A_MAX_MASTER_POS);
fprintf('    差值: %.6f mm\n', max_master_pos_actual - CONSTRAINT_A_MAX_MASTER_POS);
fprintf('    结果: %s\n', pass_fail_str(a11_pass));

% A1.2 罗拉位置限制
max_slave_pos_actual = max(complete_slave_pos);
a12_pass = max_slave_pos_actual <= CONSTRAINT_A_MAX_SLAVE_POS + 1e-6;
fprintf('  A1.2 罗拉位置限制:\n');
fprintf('    实际最大位置: %.6f mm\n', max_slave_pos_actual);
fprintf('    限制值: %.6f mm\n', CONSTRAINT_A_MAX_SLAVE_POS);
fprintf('    差值: %.6f mm\n', max_slave_pos_actual - CONSTRAINT_A_MAX_SLAVE_POS);
fprintf('    结果: %s\n', pass_fail_str(a12_pass));

% A2.1 速度非负性
min_master_vel_actual = min(master_vel);
min_slave_vel_actual = min(complete_slave_vel);
a21_pass = min_master_vel_actual >= -0.1 && min_slave_vel_actual >= -0.1;
fprintf('  A2.1 速度非负性:\n');
fprintf('    走车最小速度: %.6f mm/s\n', min_master_vel_actual);
fprintf('    罗拉最小速度: %.6f mm/s\n', min_slave_vel_actual);
fprintf('    结果: %s\n', pass_fail_str(a21_pass));

% A2.2 速度连续性
master_vel_jumps = abs(diff(master_vel));
slave_vel_jumps = abs(diff(complete_slave_vel));
max_master_vel_jump = max(master_vel_jumps);
max_slave_vel_jump = max(slave_vel_jumps);
a22_pass = max_master_vel_jump <= CONSTRAINT_A_MAX_VEL_JUMP && max_slave_vel_jump <= CONSTRAINT_A_MAX_VEL_JUMP;
fprintf('  A2.2 速度连续性:\n');
fprintf('    走车最大速度跳变: %.6f mm/s\n', max_master_vel_jump);
fprintf('    罗拉最大速度跳变: %.6f mm/s\n', max_slave_vel_jump);
fprintf('    限制值: %.6f mm/s\n', CONSTRAINT_A_MAX_VEL_JUMP);
fprintf('    结果: %s\n', pass_fail_str(a22_pass));

% 找出速度跳变的位置
if max_slave_vel_jump > CONSTRAINT_A_MAX_VEL_JUMP
    [~, jump_idx] = max(slave_vel_jumps);
    fprintf('    最大跳变位置: 索引 %d\n', jump_idx);
    fprintf('    跳变前速度: %.6f mm/s\n', complete_slave_vel(jump_idx));
    fprintf('    跳变后速度: %.6f mm/s\n', complete_slave_vel(jump_idx+1));
    fprintf('    对应时间: %.6f s\n', complete_slave_time(jump_idx));
    
    % 检查是否在拐点附近
    if abs(complete_slave_time(jump_idx) - turning_point_time) < 0.1
        fprintf('    ⚠️ 速度跳变发生在拐点附近\n');
    end
end

% 总体A类约束结果
overall_a_pass = a11_pass && a12_pass && a21_pass && a22_pass;
fprintf('\n总体A类约束结果: %s\n', pass_fail_str(overall_a_pass));

if ~overall_a_pass
    fprintf('\n失败的约束:\n');
    if ~a11_pass
        fprintf('  - A1.1 走车位置限制\n');
    end
    if ~a12_pass
        fprintf('  - A1.2 罗拉位置限制\n');
    end
    if ~a21_pass
        fprintf('  - A2.1 速度非负性\n');
    end
    if ~a22_pass
        fprintf('  - A2.2 速度连续性\n');
    end
end

fprintf('\n=== 建议修复方案 ===\n');

if ~a12_pass
    fprintf('罗拉位置超限修复:\n');
    fprintf('  - 当前超限: %.6f mm\n', max_slave_pos_actual - CONSTRAINT_A_MAX_SLAVE_POS);
    fprintf('  - 建议调整总牵伸比或增加安全裕量\n');
end

if ~a22_pass
    fprintf('速度连续性修复:\n');
    fprintf('  - 当前最大跳变: %.6f mm/s\n', max_slave_vel_jump);
    fprintf('  - 建议在拐点处增加速度平滑处理\n');
    fprintf('  - 或者调整罗拉减速参数\n');
end

function result = pass_fail_str(condition)
if condition
    result = '✅ 通过';
else
    result = '❌ 失败';
end
end
