%% 数值计算优化工具包
% 专门针对走架细纱机算法的高性能数值计算函数库
% 包含：向量化S曲线、快速积分、智能搜索等

%% 高性能S曲线计算器
function [time_vec, pos_vec, vel_vec, acc_vec] = fast_s_curve_generator(params)
%% 高性能S曲线生成器
% 特点：完全向量化、预编译友好、内存优化

% 解析计算关键参数
[time_segments, velocity_profile] = analyze_s_curve_profile(params);

% 预分配精确大小的数组
total_time = time_segments.T7;
Ts = params.Ts;
n_points = floor(total_time / Ts) + 1;

time_vec = (0:n_points-1)' * Ts;
pos_vec = zeros(n_points, 1);
vel_vec = zeros(n_points, 1);
acc_vec = zeros(n_points, 1);

% 向量化计算各段
t = time_vec;
T = time_segments;

% 使用逻辑索引进行分段计算
idx1 = t <= T.T1;
idx2 = t > T.T1 & t <= T.T2;
idx3 = t > T.T2 & t <= T.T3;
idx4 = t > T.T3 & t <= T.T4;
idx5 = t > T.T4 & t <= T.T5;
idx6 = t > T.T5 & t <= T.T6;
idx7 = t > T.T6 & t <= T.T7;

% 加加速度分段
jerk_vec = zeros(size(t));
jerk_vec(idx1) = params.jerk;
jerk_vec(idx3) = -params.jerk;
jerk_vec(idx5) = -params.jerk;
jerk_vec(idx7) = params.jerk;

% 加速度积分（向量化cumsum）
acc_vec = cumsum(jerk_vec) * Ts;
acc_vec = min(acc_vec, params.accel_pos);
acc_vec = max(acc_vec, -params.accel_neg);

% 速度积分
vel_vec = cumsum(acc_vec) * Ts;
vel_vec = max(vel_vec, 0);

% 位置积分（梯形法则向量化）
pos_vec(2:end) = cumsum((vel_vec(1:end-1) + vel_vec(2:end)) * 0.5 * Ts);

% 最终修正
vel_vec(end) = 0;
acc_vec(end) = 0;
pos_vec(end) = params.stroke;

end

function [time_segments, velocity_profile] = analyze_s_curve_profile(params)
%% S曲线轮廓分析器
% 解析计算所有时间参数，无需迭代

s = params.stroke;
v_max = params.max_speed;
a_pos = params.accel_pos;
a_neg = params.accel_neg;
j = params.jerk;

% 基本时间参数
t_j1 = a_pos / j;
t_j2 = a_neg / j;

% 判断轮廓类型
v_j1 = 0.5 * j * t_j1^2;
v_j2 = 0.5 * j * t_j2^2;

if v_j1 + v_j2 <= v_max
    % 梯形轮廓
    t_a = (v_max - v_j1) / a_pos;
    t_d = (v_max - v_j2) / a_neg;
    v_reach = v_max;
else
    % 三角形轮廓
    v_reach = sqrt(s * j);
    if v_reach > v_max
        v_reach = v_max;
        t_a = (v_max - v_j1) / a_pos;
        t_d = (v_max - v_j2) / a_neg;
    else
        t_j1 = sqrt(v_reach / j);
        t_j2 = t_j1;
        a_pos = j * t_j1;
        a_neg = a_pos;
        t_a = 0;
        t_d = 0;
        v_j1 = 0.5 * j * t_j1^2;
        v_j2 = v_j1;
    end
end

% 距离计算
s_acc = v_j1 * t_j1 + a_pos * t_a * t_j1 + 0.5 * a_pos * t_a^2 + v_j1 * t_j1;
s_dec = v_j2 * t_j2 + a_neg * t_d * t_j2 + 0.5 * a_neg * t_d^2 + v_j2 * t_j2;
s_const = s - s_acc - s_dec;
t_v = max(0, s_const / v_reach);

% 时间节点
T1 = t_j1;
T2 = T1 + t_a;
T3 = T2 + t_j1;
T4 = T3 + t_v;
T5 = T4 + t_j2;
T6 = T5 + t_d;
T7 = T6 + t_j2;

time_segments = struct('T1', T1, 'T2', T2, 'T3', T3, 'T4', T4, ...
                      'T5', T5, 'T6', T6, 'T7', T7);

velocity_profile = struct('v_max', v_reach, 'a_pos', a_pos, 'a_neg', a_neg, ...
                         't_j1', t_j1, 't_j2', t_j2, 't_a', t_a, 't_d', t_d);

end

%% 超快速拐点搜索算法
function [optimal_idx, search_data] = ultra_fast_turning_point_search(slave_pos, slave_vel, target_pos, brake_params)
%% 超快速拐点搜索
% 结合物理启发式和数值优化的混合算法

n = length(slave_pos);
search_data = struct();

% 第一阶段：物理启发式粗估计
rough_estimate = physics_based_rough_estimate(slave_vel, target_pos, brake_params);
rough_idx = max(1, min(n, round(rough_estimate * n)));

% 第二阶段：局部黄金分割搜索
search_range = round(0.1 * n);  % 在10%范围内搜索
start_idx = max(1, rough_idx - search_range);
end_idx = min(n, rough_idx + search_range);

[optimal_idx, iterations] = golden_section_search(slave_pos, slave_vel, target_pos, brake_params, start_idx, end_idx);

search_data.rough_estimate_ratio = rough_estimate;
search_data.iterations = iterations;
search_data.search_range = end_idx - start_idx + 1;
search_data.efficiency_ratio = n / iterations;

end

function rough_ratio = physics_based_rough_estimate(slave_vel, target_pos, brake_params)
%% 基于物理的粗估计
% 利用能量守恒和动力学原理快速估计

% 假设平均速度进行能量分析
avg_vel = mean(slave_vel(slave_vel > 0));
if isempty(avg_vel) || avg_vel <= 0
    rough_ratio = 0.7;  % 默认估计
    return;
end

% 基于能量的粗略刹车距离
rough_brake_distance = avg_vel^2 / (2 * brake_params.accel);

% 考虑S曲线的修正因子
s_curve_factor = 1.2;  % 经验修正因子
adjusted_brake_distance = rough_brake_distance * s_curve_factor;

% 估计拐点位置
estimated_turning_point = target_pos - adjusted_brake_distance;

% 转换为比例
if target_pos > 0
    rough_ratio = estimated_turning_point / target_pos;
else
    rough_ratio = 0.7;  % 默认估计
end

% 限制在合理范围内
rough_ratio = max(0.3, min(0.9, rough_ratio));

end

function [optimal_idx, iterations] = golden_section_search(slave_pos, slave_vel, target_pos, brake_params, start_idx, end_idx)
%% 黄金分割搜索算法
% 在指定范围内快速找到最优拐点

phi = (1 + sqrt(5)) / 2;  % 黄金比例
resphi = 2 - phi;

% 初始化搜索区间
a = start_idx;
b = end_idx;
tol = 1;  % 索引容差

iterations = 0;
max_iterations = 20;

% 计算初始内部点
c = a + resphi * (b - a);
d = a + (1 - resphi) * (b - a);

% 计算初始函数值
fc = calculate_turning_point_error(round(c), slave_pos, slave_vel, target_pos, brake_params);
fd = calculate_turning_point_error(round(d), slave_pos, slave_vel, target_pos, brake_params);

% 黄金分割迭代
while abs(b - a) > tol && iterations < max_iterations
    iterations = iterations + 1;
    
    if fc < fd
        b = d;
        d = c;
        fd = fc;
        c = a + resphi * (b - a);
        fc = calculate_turning_point_error(round(c), slave_pos, slave_vel, target_pos, brake_params);
    else
        a = c;
        c = d;
        fc = fd;
        d = a + (1 - resphi) * (b - a);
        fd = calculate_turning_point_error(round(d), slave_pos, slave_vel, target_pos, brake_params);
    end
end

% 返回最优索引
optimal_idx = round((a + b) / 2);
optimal_idx = max(start_idx, min(end_idx, optimal_idx));

end

function error_val = calculate_turning_point_error(idx, slave_pos, slave_vel, target_pos, brake_params)
%% 拐点误差计算
% 快速计算指定拐点的误差

if idx < 1 || idx > length(slave_pos) || slave_vel(idx) <= 0
    error_val = inf;
    return;
end

% 快速刹车距离计算
brake_distance = fast_brake_distance_calculation(slave_vel(idx), brake_params);

% 期望停止位置
expected_stop = slave_pos(idx) + brake_distance;

% 计算误差
error_val = abs(expected_stop - target_pos);

end

function brake_dist = fast_brake_distance_calculation(v0, brake_params)
%% 快速刹车距离计算
% 使用解析公式，避免数值积分

if v0 <= 0
    brake_dist = 0;
    return;
end

a_decel = brake_params.accel;
j_max = brake_params.jerk;

% S曲线减速距离解析公式
t_j = a_decel / j_max;

if v0 * j_max < a_decel^2
    % 三角形减速轮廓
    t_j = sqrt(v0 / j_max);
    brake_dist = (2/3) * v0 * t_j;
else
    % 梯形减速轮廓
    t_const = v0 / a_decel - t_j;
    brake_dist = v0^2 / (2 * a_decel) + (a_decel * t_j^2) / 6;
end

end

%% 高效轨迹拼接器
function complete_trajectory = efficient_trajectory_combiner(sync_data, decel_data, turning_point_info)
%% 高效轨迹拼接器
% 零拷贝拼接，最小化内存分配

sync_len = turning_point_info.index;
decel_len = length(decel_data.time);
total_len = sync_len + decel_len - 1;

% 预分配目标数组
complete_trajectory = struct();
complete_trajectory.time = zeros(total_len, 1);
complete_trajectory.position = zeros(total_len, 1);
complete_trajectory.velocity = zeros(total_len, 1);

% 直接内存拷贝（MATLAB优化）
complete_trajectory.time(1:sync_len) = sync_data.time(1:sync_len);
complete_trajectory.position(1:sync_len) = sync_data.position(1:sync_len);
complete_trajectory.velocity(1:sync_len) = sync_data.velocity(1:sync_len);

% 调整减速段时间基准
decel_time_adjusted = decel_data.time(2:end) + turning_point_info.time;
decel_pos_adjusted = decel_data.position(2:end) + turning_point_info.position;

% 拼接减速段
idx_start = sync_len + 1;
idx_end = total_len;
complete_trajectory.time(idx_start:idx_end) = decel_time_adjusted;
complete_trajectory.position(idx_start:idx_end) = decel_pos_adjusted;
complete_trajectory.velocity(idx_start:idx_end) = decel_data.velocity(2:end);

end

%% 向量化质量验证器
function validation_result = vectorized_quality_validator(trajectory, params)
%% 向量化质量验证器
% 使用向量化操作快速验证轨迹质量

validation_result = struct();

% 基本误差检查
final_pos = trajectory.position(end);
target_pos = params.stroke / params.ratio_total;
validation_result.position_error = abs(final_pos - target_pos);

% 向量化连续性检查
time_diff = diff(trajectory.time);
pos_diff = diff(trajectory.position);
vel_diff = diff(trajectory.velocity);

% 时间连续性
validation_result.time_continuity = all(time_diff > 0) && std(time_diff) < params.Ts * 0.1;

% 速度连续性
max_vel_jump = max(abs(vel_diff));
validation_result.velocity_continuity = max_vel_jump < 10;  % mm/s

% 位置单调性
validation_result.position_monotonic = all(pos_diff >= -1e-6);

% 物理合理性检查
max_vel = max(trajectory.velocity);
validation_result.velocity_reasonable = max_vel <= params.max_speed * 1.1;

% 综合质量评分
quality_score = 0;
if validation_result.position_error < 1.0, quality_score = quality_score + 25; end
if validation_result.time_continuity, quality_score = quality_score + 25; end
if validation_result.velocity_continuity, quality_score = quality_score + 25; end
if validation_result.position_monotonic, quality_score = quality_score + 25; end

validation_result.quality_score = quality_score;
validation_result.overall_pass = quality_score >= 75;

end

%% 内存优化管理器
function optimized_data = memory_optimization_manager(raw_data, optimization_level)
%% 内存优化管理器
% 根据需求级别优化数据存储

switch optimization_level
    case 'minimal'
        % 只保留关键采样点
        sample_ratio = 10;  % 每10个点采样1个
        indices = 1:sample_ratio:length(raw_data.time);
        indices = [indices, length(raw_data.time)];  % 确保包含最后一个点
        
        optimized_data.time = raw_data.time(indices);
        optimized_data.position = raw_data.position(indices);
        optimized_data.velocity = raw_data.velocity(indices);
        
    case 'efficient'
        % 智能采样：关键区域高密度，平坦区域低密度
        optimized_data = adaptive_sampling(raw_data);
        
    case 'full'
        % 保留全部数据
        optimized_data = raw_data;
        
    otherwise
        optimized_data = raw_data;
end

% 数据压缩（转换为单精度）
if isfield(optimized_data, 'time')
    optimized_data.time = single(optimized_data.time);
    optimized_data.position = single(optimized_data.position);
    optimized_data.velocity = single(optimized_data.velocity);
end

end

function adaptive_data = adaptive_sampling(raw_data)
%% 自适应采样
% 在变化剧烈的区域保持高采样率，平坦区域降低采样率

n = length(raw_data.time);
if n <= 100
    adaptive_data = raw_data;
    return;
end

% 计算速度和加速度的变化率
vel_change = abs(diff(raw_data.velocity));
pos_change = abs(diff(raw_data.position));

% 归一化变化率
vel_change_norm = vel_change / max(vel_change);
pos_change_norm = pos_change / max(pos_change);

% 综合变化率
total_change = vel_change_norm + pos_change_norm;

% 基于变化率确定采样密度
sample_density = 1 + total_change * 4;  % 1-5倍密度
sample_indices = [];

i = 1;
while i <= n-1
    sample_indices = [sample_indices, i];
    
    % 根据当前点的变化率决定下一个采样点
    if i <= length(sample_density)
        skip = max(1, round(5 / sample_density(i)));
    else
        skip = 5;
    end
    
    i = i + skip;
end

% 确保包含最后一个点
sample_indices = [sample_indices, n];
sample_indices = unique(sample_indices);

adaptive_data.time = raw_data.time(sample_indices);
adaptive_data.position = raw_data.position(sample_indices);
adaptive_data.velocity = raw_data.velocity(sample_indices);

end

%% 性能监控器
function performance_metrics = performance_monitor()
%% 性能监控器
% 实时监控算法性能指标

persistent start_time;
persistent peak_memory;

if isempty(start_time)
    start_time = tic;
    peak_memory = 0;
end

% 获取当前内存使用
[user_mem, sys_mem] = memory;
current_memory = user_mem.MemUsedMATLAB / 1024 / 1024;  % MB
peak_memory = max(peak_memory, current_memory);

% 计算执行时间
elapsed_time = toc(start_time);

performance_metrics = struct();
performance_metrics.elapsed_time = elapsed_time;
performance_metrics.current_memory = current_memory;
performance_metrics.peak_memory = peak_memory;
performance_metrics.timestamp = now;

% 重置计时器
start_time = tic;

end

%% 使用示例和测试函数
function run_optimization_toolkit_demo()
%% 优化工具包演示
fprintf('=== 数值计算优化工具包演示 ===\n');

% 测试参数
params = struct();
params.stroke = 4000;
params.max_speed = 600;
params.accel_pos = 300;
params.accel_neg = 800;
params.jerk = 600;
params.Ts = 0.004;
params.ratio_total = 1.5;

% 测试高性能S曲线生成
fprintf('测试高性能S曲线生成...\n');
tic;
[time_vec, pos_vec, vel_vec, acc_vec] = fast_s_curve_generator(params);
s_curve_time = toc;
fprintf('  S曲线生成耗时: %.3f秒, 数据点: %d\n', s_curve_time, length(time_vec));

% 测试快速拐点搜索
fprintf('测试超快速拐点搜索...\n');
slave_pos = pos_vec / 1.2;
slave_vel = vel_vec / 1.2;
target_pos = params.stroke / params.ratio_total;
brake_params = struct('accel', 2000, 'jerk', 12500);

tic;
[optimal_idx, search_data] = ultra_fast_turning_point_search(slave_pos, slave_vel, target_pos, brake_params);
search_time = toc;
fprintf('  拐点搜索耗时: %.3f秒, 效率提升: %.1fx\n', search_time, search_data.efficiency_ratio);

% 测试内存优化
fprintf('测试内存优化...\n');
raw_data = struct('time', time_vec, 'position', pos_vec, 'velocity', vel_vec);
original_size = whos('raw_data');

optimized_data = memory_optimization_manager(raw_data, 'efficient');
optimized_size = whos('optimized_data');

memory_saving = (1 - optimized_size.bytes / original_size.bytes) * 100;
fprintf('  内存节省: %.1f%%\n', memory_saving);

fprintf('=== 工具包演示完成 ===\n');

end