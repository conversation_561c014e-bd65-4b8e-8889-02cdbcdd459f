function [time_vec, vel_vec] = generate_position_controlled_decel_profile(v0, a_decel, j_max, target_distance, Ts)
%% 位置控制的减速轨迹生成器
% 专门为A类约束修复设计，确保减速轨迹不超过指定距离
% 输入：
%   v0 - 初始速度 (mm/s)
%   a_decel - 减速度 (mm/s²)
%   j_max - 最大加加速度 (mm/s³)
%   target_distance - 目标减速距离 (mm)
%   Ts - 采样时间 (s)
% 输出：
%   time_vec - 时间向量 (s)
%   vel_vec - 速度向量 (mm/s)

if v0 <= 0.001 || target_distance <= 0.001
    time_vec = 0;
    vel_vec = 0;
    return;
end

% 🚨 关键：根据目标距离反推最优减速参数
% 如果按照原参数减速距离会超过目标距离，则调整参数

% 计算理论减速距离
theoretical_distance = v0^2 / (2 * a_decel);

if theoretical_distance > target_distance
    % 需要调整减速参数以适应距离限制
    fprintf('    ⚠️ 理论减速距离%.3f mm > 目标距离%.3f mm，调整参数\n', theoretical_distance, target_distance);
    
    % 重新计算所需的减速度
    required_accel = v0^2 / (2 * target_distance * 0.9);  % 0.9安全系数
    a_decel = min(a_decel, required_accel);
    
    % 相应调整加加速度
    j_max = min(j_max, a_decel * 20);  % 保持合理的加加速度比例
    
    fprintf('    调整后参数: a=%.1f mm/s², j=%.1f mm/s³\n', a_decel, j_max);
end

% 计算S曲线减速参数
t_j = a_decel / j_max;

if v0 * j_max < a_decel^2
    % 三角形减速轮廓
    t_j = sqrt(v0 / j_max);
    a_actual = j_max * t_j;
    t_const = 0;
    T_total = 2 * t_j;
else
    % 梯形减速轮廓
    a_actual = a_decel;
    t_const = v0 / a_actual - t_j;
    T_total = 2 * t_j + t_const;
end

% 时间节点
T1 = t_j;
T2 = T1 + t_const;
T3 = T2 + t_j;

% 生成时间序列
time_vec = (0:Ts:T_total)';
N = length(time_vec);

% 确保最后一个时间点
if abs(time_vec(end) - T_total) > Ts/2
    time_vec = [time_vec; T_total];
    N = N + 1;
end

vel_vec = zeros(N, 1);
acc_vec = zeros(N, 1);
pos_vec = zeros(N, 1);  % 用于位置监控

vel_vec(1) = v0;
pos_vec(1) = 0;

% 生成减速轨迹，同时监控位置
for i = 2:N
    t = time_vec(i-1);
    
    % 确定加加速度
    if t < T1
        jerk = -j_max;
    elseif t < T2
        jerk = 0;
    elseif t < T3
        jerk = j_max;
    else
        jerk = 0;
    end
    
    % 更新加速度
    acc_vec(i) = acc_vec(i-1) + jerk * Ts;
    acc_vec(i) = max(acc_vec(i), -a_actual);
    acc_vec(i) = min(acc_vec(i), 0);
    
    % 更新速度
    vel_vec(i) = vel_vec(i-1) + acc_vec(i-1) * Ts;
    vel_vec(i) = max(vel_vec(i), 0);
    
    % 更新位置（用于监控）
    pos_vec(i) = pos_vec(i-1) + (vel_vec(i-1) + vel_vec(i)) * 0.5 * Ts;
    
    % 🚨 关键：位置限制保护
    if pos_vec(i) >= target_distance
        % 达到目标距离，立即停止
        pos_vec(i) = target_distance;
        vel_vec(i) = 0;
        
        % 截断后续所有点
        if i < N
            time_vec = time_vec(1:i);
            vel_vec = vel_vec(1:i);
            pos_vec = pos_vec(1:i);
        end
        break;
    end
    
    % 平滑结束：当速度接近0时自然停止
    if vel_vec(i) < 0.1
        vel_vec(i) = 0;
        if i < N
            vel_vec((i+1):end) = 0;
        end
        break;
    end
end

% 最终确保速度为0
vel_vec(end) = 0;

% 🚨 最终位置验证
final_distance = pos_vec(end);
if final_distance > target_distance + 0.001
    warning('位置控制减速轨迹超出目标距离: %.6f mm > %.6f mm', final_distance, target_distance);
    
    % 强制缩放整个轨迹
    scale_factor = target_distance / final_distance;
    fprintf('    应用缩放因子: %.6f\n', scale_factor);
    
    % 重新生成缩放后的轨迹（简化处理）
    time_vec = time_vec * scale_factor;
    % 速度保持不变，通过时间缩放来控制距离
end

fprintf('    位置控制减速轨迹: 距离%.3f mm, 时间%.3f s\n', final_distance, time_vec(end));

end
