%% 测试新的全新架构优化版算法
% 简化测试脚本，验证核心功能

clear; clc; close all;

fprintf('=== 测试走架细纱机牵伸控制算法 - 全新架构优化版 V3.0 ===\n');

%% 基本参数设置
HMI_r64_Gear_ZouChe_position = 4000.0;      % 走车行程 (mm)
HMI_r64_Gear_ZouChe_velocity = 600.0;       % 走车最大速度 (mm/s)
HMI_r64_Gear_ZouChe_positiveaccel = 300.0;  % 走车正向加速度 (mm/s²)
HMI_r64_Gear_ZouChe_negativeaccel = 800.0;  % 走车负向加速度 (mm/s²)
HMI_r64_Gear_ZouChe_jerk = 600.0;           % 走车加加速度 (mm/s³)

HMI_r64_Gear_LuoLa_negativeaccel = 2000.0;  % 罗拉独立刹车加速度 (mm/s²)
HMI_r64_Gear_LuoLa_jerk = 12500.0;          % 罗拉独立刹车加加速度 (mm/s³)

HMI_r64QianShen_FenSan = 1.2;               % 分散牵伸比
HMI_r64QianShen_All = 1.5;                  % 总牵伸比

Ts = 0.004;                                  % 采样时间 (s)

fprintf('参数配置完成\n');

%% 测试"黄金标准"基准模块1：S曲线生成器
fprintf('\n测试S曲线生成器...\n');
tic;

try
    [master_time, master_pos, master_vel, master_acc] = generate_golden_standard_s_curve(...
        HMI_r64_Gear_ZouChe_position, HMI_r64_Gear_ZouChe_velocity, ...
        HMI_r64_Gear_ZouChe_positiveaccel, HMI_r64_Gear_ZouChe_negativeaccel, ...
        HMI_r64_Gear_ZouChe_jerk, Ts);
    
    fprintf('  ✅ S曲线生成成功\n');
    fprintf('  数据点: %d, 总时间: %.3f s\n', length(master_time), master_time(end));
    fprintf('  最终位置: %.6f mm (误差: %.6f mm)\n', master_pos(end), abs(master_pos(end) - HMI_r64_Gear_ZouChe_position));
    fprintf('  最大速度: %.3f mm/s\n', max(master_vel));
    
catch ME
    fprintf('  ❌ S曲线生成失败: %s\n', ME.message);
    return;
end

s_curve_time = toc;

%% 测试"黄金标准"基准模块2：刹车距离计算器
fprintf('\n测试刹车距离计算器...\n');

test_velocities = [100, 300, 500];
for i = 1:length(test_velocities)
    v_test = test_velocities(i);
    brake_dist = calculate_golden_standard_braking_distance(...
        v_test, HMI_r64_Gear_LuoLa_negativeaccel, HMI_r64_Gear_LuoLa_jerk, Ts);
    fprintf('  速度 %.0f mm/s → 刹车距离 %.3f mm\n', v_test, brake_dist);
end

%% 测试"黄金标准"基准模块3：减速轨迹生成器
fprintf('\n测试减速轨迹生成器...\n');

test_vel = 400.0;
[decel_time, decel_vel] = generate_golden_standard_decel_profile(...
    test_vel, HMI_r64_Gear_LuoLa_negativeaccel, HMI_r64_Gear_LuoLa_jerk, Ts);

fprintf('  初始速度: %.1f mm/s\n', test_vel);
fprintf('  减速时间: %.3f s\n', decel_time(end));
fprintf('  最终速度: %.6f mm/s\n', decel_vel(end));

%% 简化的拐点搜索测试
fprintf('\n测试拐点搜索...\n');

% 生成理想同步轨迹
ideal_slave_pos = master_pos / HMI_r64QianShen_FenSan;
ideal_slave_vel = master_vel / HMI_r64QianShen_FenSan;
slave_final_target = HMI_r64_Gear_ZouChe_position / HMI_r64QianShen_All;

% 简化的拐点搜索
N = length(master_time);
best_error = inf;
best_index = 1;

for i = round(N*0.3):round(N*0.9)  % 在30%-90%范围内搜索
    if ideal_slave_vel(i) <= 0.1
        continue;
    end
    
    brake_distance = calculate_golden_standard_braking_distance(...
        ideal_slave_vel(i), HMI_r64_Gear_LuoLa_negativeaccel, HMI_r64_Gear_LuoLa_jerk, Ts);
    
    expected_stop = ideal_slave_pos(i) + brake_distance;
    error_val = abs(expected_stop - slave_final_target);
    
    if error_val < best_error
        best_error = error_val;
        best_index = i;
    end
end

turning_point_time = master_time(best_index);
turning_point_pos = ideal_slave_pos(best_index);
turning_point_vel = ideal_slave_vel(best_index);

fprintf('  拐点位置: %.6f mm\n', turning_point_pos);
fprintf('  拐点时刻: %.6f s\n', turning_point_time);
fprintf('  拐点速度: %.6f mm/s\n', turning_point_vel);
fprintf('  拐点误差: %.6f mm\n', best_error);

%% 生成完整轨迹
fprintf('\n生成完整轨迹...\n');

% 生成减速轨迹
[decel_time, decel_vel] = generate_golden_standard_decel_profile(...
    turning_point_vel, HMI_r64_Gear_LuoLa_negativeaccel, HMI_r64_Gear_LuoLa_jerk, Ts);

% 计算减速段位置
decel_pos = zeros(size(decel_vel));
decel_pos(1) = turning_point_pos;
for i = 2:length(decel_pos)
    decel_pos(i) = decel_pos(i-1) + (decel_vel(i-1) + decel_vel(i)) * 0.5 * Ts;
end

% 调整时间基准
decel_time_absolute = decel_time + turning_point_time;

% 拼接完整轨迹
complete_slave_time = [master_time(1:best_index); decel_time_absolute(2:end)];
complete_slave_pos = [ideal_slave_pos(1:best_index); decel_pos(2:end)];
complete_slave_vel = [ideal_slave_vel(1:best_index); decel_vel(2:end)];

% 确保最终位置精确
complete_slave_pos(end) = slave_final_target;
complete_slave_vel(end) = 0;

%% 质量验证
fprintf('\n质量验证...\n');

% 验证牵伸比
actual_total_ratio = HMI_r64_Gear_ZouChe_position / complete_slave_pos(end);
ratio_error_percent = abs(actual_total_ratio - HMI_r64QianShen_All) / HMI_r64QianShen_All * 100;

actual_distributed_ratio = master_pos(best_index) / complete_slave_pos(best_index);
distributed_error_percent = abs(actual_distributed_ratio - HMI_r64QianShen_FenSan) / HMI_r64QianShen_FenSan * 100;

final_position_error = abs(complete_slave_pos(end) - slave_final_target);

fprintf('  总牵伸比: %.8f (误差: %.4f%%)\n', actual_total_ratio, ratio_error_percent);
fprintf('  分散牵伸比: %.8f (误差: %.4f%%)\n', actual_distributed_ratio, distributed_error_percent);
fprintf('  位置误差: %.6f mm\n', final_position_error);

% 验收判断
ratio_pass = ratio_error_percent < 1.0;
distributed_pass = distributed_error_percent < 1.0;
position_pass = final_position_error < 0.5;
overall_pass = ratio_pass && distributed_pass && position_pass;

if overall_pass
    fprintf('  ✅ 算法验证通过！\n');
else
    fprintf('  ❌ 算法验证失败\n');
end

%% 生成验证图表
fprintf('\n生成验证图表...\n');

figure('Name', '新算法验证结果', 'Position', [100, 100, 1200, 800]);

% 子图1: 位置轨迹
subplot(2,3,1);
plot(master_time, master_pos, 'b-', 'LineWidth', 2);
hold on;
plot(complete_slave_time, complete_slave_pos, 'r-', 'LineWidth', 2);
line([turning_point_time, turning_point_time], [0, max(master_pos)], ...
    'Color', 'g', 'LineStyle', '--', 'LineWidth', 2);
title('位置轨迹对比');
xlabel('时间 (s)'); ylabel('位置 (mm)');
legend('走车', '罗拉', '拐点', 'Location', 'best');
grid on;

% 子图2: 速度轨迹
subplot(2,3,2);
plot(master_time, master_vel, 'b-', 'LineWidth', 2);
hold on;
plot(complete_slave_time, complete_slave_vel, 'r-', 'LineWidth', 2);
line([turning_point_time, turning_point_time], [0, max(master_vel)], ...
    'Color', 'g', 'LineStyle', '--', 'LineWidth', 2);
title('速度轨迹对比');
xlabel('时间 (s)'); ylabel('速度 (mm/s)');
legend('走车', '罗拉', '拐点', 'Location', 'best');
grid on;

% 子图3: 实时牵伸比
subplot(2,3,3);
master_pos_interp = interp1(master_time, master_pos, complete_slave_time, 'linear', 'extrap');
realtime_ratio = master_pos_interp ./ complete_slave_pos;
plot(complete_slave_time, realtime_ratio, 'k-', 'LineWidth', 2);
hold on;
line([0, complete_slave_time(end)], [HMI_r64QianShen_FenSan, HMI_r64QianShen_FenSan], ...
    'Color', 'r', 'LineStyle', '--', 'LineWidth', 2);
line([0, complete_slave_time(end)], [HMI_r64QianShen_All, HMI_r64QianShen_All], ...
    'Color', 'b', 'LineStyle', '--', 'LineWidth', 2);
title('实时牵伸比变化');
xlabel('时间 (s)'); ylabel('牵伸比');
legend('实时比', '分散牵伸比', '总牵伸比', 'Location', 'best');
grid on;

% 子图4: 精度分析
subplot(2,3,4);
precision_data = [ratio_error_percent, distributed_error_percent, final_position_error];
precision_labels = {'总牵伸比(%)', '分散牵伸比(%)', '位置误差(mm)'};
bar(precision_data, 'FaceColor', [0.8, 0.2, 0.2]);
set(gca, 'XTickLabel', precision_labels);
title('精度分析');
ylabel('误差值');
grid on;

% 子图5: 验收状态
subplot(2,3,5);
pass_indicators = [ratio_pass, distributed_pass, position_pass];
pass_labels = {'总牵伸比', '分散牵伸比', '位置精度'};
colors = zeros(length(pass_indicators), 3);
for i = 1:length(pass_indicators)
    if pass_indicators(i)
        colors(i, :) = [0.2, 0.8, 0.2];  % 绿色
    else
        colors(i, :) = [0.8, 0.2, 0.2];  % 红色
    end
end

for i = 1:length(pass_indicators)
    barh(i, 1, 'FaceColor', colors(i, :));
    hold on;
end
set(gca, 'YTickLabel', pass_labels);
xlim([0, 1.2]);
title('验收状态');
grid on;

% 子图6: 关键数据
subplot(2,3,6);
key_data_text = {
    '关键输出数据:'
    ''
    sprintf('拐点位置: %.3f mm', turning_point_pos)
    sprintf('拐点时刻: %.3f s', turning_point_time)
    sprintf('拐点速度: %.3f mm/s', turning_point_vel)
    sprintf('总牵伸比: %.6f', actual_total_ratio)
    sprintf('分散牵伸比: %.6f', actual_distributed_ratio)
    sprintf('位置误差: %.3f mm', final_position_error)
    ''
    sprintf('总执行时间: %.3f s', s_curve_time)
    sprintf('验收状态: %s', pass_fail_str(overall_pass))
};
text(0.05, 0.5, key_data_text, 'FontSize', 10, 'VerticalAlignment', 'middle', 'FontName', 'FixedWidth');
axis off;
title('关键数据');

sgtitle('走架细纱机算法 - 全新架构优化版 V3.0 验证结果', 'FontSize', 14, 'FontWeight', 'bold');

fprintf('  ✅ 图表生成完成\n');

%% 总结
fprintf('\n=== 测试总结 ===\n');
fprintf('🚀 算法执行时间: %.3f s\n', s_curve_time);
fprintf('🎯 验收状态: %s\n', pass_fail_str(overall_pass));
fprintf('📊 关键精度指标:\n');
fprintf('  • 总牵伸比误差: %.4f%%\n', ratio_error_percent);
fprintf('  • 分散牵伸比误差: %.4f%%\n', distributed_error_percent);
fprintf('  • 位置误差: %.3f mm\n', final_position_error);

if overall_pass
    fprintf('\n🎉 新算法验证成功！可以进行进一步测试。\n');
else
    fprintf('\n⚠️ 新算法需要进一步优化。\n');
end

fprintf('================================\n');

%% 辅助函数
function result = pass_fail_str(condition)
if condition
    result = '✅ 通过';
else
    result = '❌ 失败';
end
end
